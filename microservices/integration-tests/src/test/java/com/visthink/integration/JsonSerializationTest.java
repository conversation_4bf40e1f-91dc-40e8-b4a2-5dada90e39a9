package com.visthink.integration;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.junit.jupiter.api.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static io.restassured.RestAssured.given;
import static org.hamcrest.Matchers.*;
import static org.junit.jupiter.api.Assertions.*;

/**
 * JSON序列化集成测试
 *
 * 测试场景：
 * 1. 基础数据类型序列化测试
 * 2. 复杂对象序列化测试
 * 3. 日期时间序列化测试
 * 4. 中文字符序列化测试
 * 5. 大数据量序列化性能测试
 * 6. 微服务间JSON数据传输测试
 * 7. 错误响应JSON格式验证
 *
 * <AUTHOR>
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@DisplayName("JSON序列化集成测试")
public class JsonSerializationTest {

    private static final String ORDER_SERVICE_URL = "http://localhost:8084";
    private static final String PRODUCT_SERVICE_URL = "http://localhost:8082";
    private static final String TENANT_HEADER = "X-Tenant-Id";
    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final int MAX_RESPONSE_TIME = 500;

    private static ObjectMapper objectMapper;

    @BeforeAll
    static void setupClass() {
        System.out.println("=== 开始JSON序列化集成测试 ===");
        RestAssured.enableLoggingOfRequestAndResponseIfValidationFails();
        
        // 初始化ObjectMapper，配置与微服务相同的序列化设置
        objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
    }

    @AfterAll
    static void tearDownClass() {
        System.out.println("=== JSON序列化集成测试完成 ===");
    }

    /**
     * 测试1：基础数据类型序列化测试
     */
    @Test
    @Order(1)
    @DisplayName("1. 基础数据类型序列化测试")
    void testBasicDataTypeSerialization() {
        System.out.println("测试1：基础数据类型序列化测试");

        // 构造包含各种基础数据类型的测试数据
        Map<String, Object> testData = Map.of(
                "stringValue", "测试字符串",
                "intValue", 123,
                "longValue", 123456789L,
                "doubleValue", 123.456,
                "booleanValue", true,
                "nullValue", (Object) null
        );

        try {
            // 测试JSON序列化
            String jsonString = objectMapper.writeValueAsString(testData);
            System.out.println("序列化结果: " + jsonString);

            // 测试JSON反序列化
            Map<String, Object> deserializedData = objectMapper.readValue(jsonString, Map.class);
            System.out.println("反序列化结果: " + deserializedData);

            // 验证序列化和反序列化的一致性
            assertEquals(testData.get("stringValue"), deserializedData.get("stringValue"));
            assertEquals(testData.get("intValue"), deserializedData.get("intValue"));
            assertEquals(testData.get("booleanValue"), deserializedData.get("booleanValue"));

            System.out.println("✅ 基础数据类型序列化测试成功");

        } catch (JsonProcessingException e) {
            fail("❌ 基础数据类型序列化测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试2：复杂对象序列化测试
     */
    @Test
    @Order(2)
    @DisplayName("2. 复杂对象序列化测试")
    void testComplexObjectSerialization() {
        System.out.println("测试2：复杂对象序列化测试");

        // 构造复杂的嵌套对象
        Map<String, Object> complexObject = Map.of(
                "orderId", UUID.randomUUID().toString(),
                "customer", Map.of(
                        "id", 1L,
                        "name", "张三",
                        "email", "<EMAIL>",
                        "address", Map.of(
                                "province", "北京市",
                                "city", "北京市",
                                "district", "朝阳区",
                                "detail", "某某街道123号"
                        )
                ),
                "items", List.of(
                        Map.of(
                                "productId", 1L,
                                "productName", "测试商品1",
                                "quantity", 2,
                                "unitPrice", 99.99,
                                "totalPrice", 199.98
                        ),
                        Map.of(
                                "productId", 2L,
                                "productName", "测试商品2",
                                "quantity", 1,
                                "unitPrice", 299.99,
                                "totalPrice", 299.99
                        )
                ),
                "totalAmount", 499.97,
                "status", "PENDING",
                "metadata", Map.of(
                        "source", "WEB",
                        "channel", "PC",
                        "tags", List.of("新客户", "促销订单")
                )
        );

        try {
            long startTime = System.currentTimeMillis();

            // 测试复杂对象序列化
            String jsonString = objectMapper.writeValueAsString(complexObject);
            long serializationTime = System.currentTimeMillis() - startTime;

            System.out.println("复杂对象序列化时间: " + serializationTime + "ms");
            System.out.println("序列化结果长度: " + jsonString.length() + " 字符");

            // 验证序列化性能
            assertTrue(serializationTime < 100, "复杂对象序列化时间应小于100ms");

            startTime = System.currentTimeMillis();

            // 测试复杂对象反序列化
            Map<String, Object> deserializedObject = objectMapper.readValue(jsonString, Map.class);
            long deserializationTime = System.currentTimeMillis() - startTime;

            System.out.println("复杂对象反序列化时间: " + deserializationTime + "ms");

            // 验证反序列化性能
            assertTrue(deserializationTime < 100, "复杂对象反序列化时间应小于100ms");

            // 验证数据完整性
            assertEquals(complexObject.get("orderId"), deserializedObject.get("orderId"));
            assertEquals(complexObject.get("totalAmount"), deserializedObject.get("totalAmount"));

            System.out.println("✅ 复杂对象序列化测试成功");

        } catch (JsonProcessingException e) {
            fail("❌ 复杂对象序列化测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试3：日期时间序列化测试
     */
    @Test
    @Order(3)
    @DisplayName("3. 日期时间序列化测试")
    void testDateTimeSerialization() {
        System.out.println("测试3：日期时间序列化测试");

        LocalDateTime now = LocalDateTime.now();
        String expectedDateString = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"));

        Map<String, Object> dateTimeData = Map.of(
                "createdTime", now,
                "updatedTime", now.plusHours(1),
                "dateString", expectedDateString,
                "timestamp", System.currentTimeMillis()
        );

        try {
            // 测试日期时间序列化
            String jsonString = objectMapper.writeValueAsString(dateTimeData);
            System.out.println("日期时间序列化结果: " + jsonString);

            // 验证JSON中包含正确的日期格式
            assertTrue(jsonString.contains(expectedDateString.substring(0, 10)), 
                      "序列化结果应包含正确的日期格式");

            // 测试反序列化
            JsonNode jsonNode = objectMapper.readTree(jsonString);
            assertNotNull(jsonNode.get("createdTime"), "createdTime字段应该存在");
            assertNotNull(jsonNode.get("timestamp"), "timestamp字段应该存在");

            System.out.println("✅ 日期时间序列化测试成功");

        } catch (JsonProcessingException e) {
            fail("❌ 日期时间序列化测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试4：中文字符序列化测试
     */
    @Test
    @Order(4)
    @DisplayName("4. 中文字符序列化测试")
    void testChineseCharacterSerialization() {
        System.out.println("测试4：中文字符序列化测试");

        Map<String, Object> chineseData = Map.of(
                "productName", "高端智能手机",
                "description", "这是一款功能强大的智能手机，具有优秀的拍照功能和长续航能力。",
                "category", "电子产品",
                "brand", "华为",
                "tags", List.of("智能", "高端", "拍照", "续航"),
                "specifications", Map.of(
                        "屏幕尺寸", "6.1英寸",
                        "处理器", "麒麟9000",
                        "内存", "8GB",
                        "存储", "256GB"
                )
        );

        try {
            // 测试中文字符序列化
            String jsonString = objectMapper.writeValueAsString(chineseData);
            System.out.println("中文字符序列化结果: " + jsonString);

            // 验证中文字符正确编码
            assertTrue(jsonString.contains("高端智能手机"), "序列化结果应包含正确的中文字符");
            assertTrue(jsonString.contains("华为"), "序列化结果应包含正确的中文品牌名");

            // 测试反序列化
            Map<String, Object> deserializedData = objectMapper.readValue(jsonString, Map.class);
            assertEquals("高端智能手机", deserializedData.get("productName"));
            assertEquals("华为", deserializedData.get("brand"));

            System.out.println("✅ 中文字符序列化测试成功");

        } catch (JsonProcessingException e) {
            fail("❌ 中文字符序列化测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试5：微服务间JSON数据传输测试
     */
    @Test
    @Order(5)
    @DisplayName("5. 微服务间JSON数据传输测试")
    void testMicroserviceJsonCommunication() {
        System.out.println("测试5：微服务间JSON数据传输测试");

        // 构造订单数据，测试订单服务和商品服务之间的JSON通信
        Map<String, Object> orderData = Map.of(
                "businessId", "JSON_TEST_" + UUID.randomUUID().toString().substring(0, 8),
                "customerId", 1L,
                "customerName", "JSON测试用户",
                "orderItems", List.of(
                        Map.of(
                                "productId", 1L,
                                "productName", "JSON测试商品",
                                "quantity", 1,
                                "unitPrice", 99.99,
                                "specifications", Map.of(
                                        "颜色", "红色",
                                        "尺寸", "L",
                                        "材质", "纯棉"
                                )
                        )
                ),
                "totalAmount", 99.99,
                "remark", "JSON序列化测试订单",
                "metadata", Map.of(
                        "testType", "JSON_SERIALIZATION",
                        "timestamp", System.currentTimeMillis()
                )
        );

        try {
            long startTime = System.currentTimeMillis();

            // 发送JSON数据到订单服务
            Response response = given()
                    .header(TENANT_HEADER, DEFAULT_TENANT_ID)
                    .contentType(ContentType.JSON)
                    .body(orderData)
                    .when()
                    .post(ORDER_SERVICE_URL + "/api/orders")
                    .then()
                    .statusCode(anyOf(is(200), is(201), is(400), is(422), is(500))) // 允许多种状态码
                    .extract().response();

            long responseTime = System.currentTimeMillis() - startTime;

            System.out.println("JSON通信响应时间: " + responseTime + "ms");
            System.out.println("响应状态码: " + response.getStatusCode());
            System.out.println("响应内容: " + response.asString());

            // 验证响应时间
            assertTrue(responseTime < MAX_RESPONSE_TIME,
                    "JSON通信响应时间应小于" + MAX_RESPONSE_TIME + "ms，实际: " + responseTime + "ms");

            // 验证响应是有效的JSON格式
            if (response.getStatusCode() == 200 || response.getStatusCode() == 201) {
                assertNotNull(response.jsonPath(), "响应应该是有效的JSON格式");
                
                // 验证响应中包含中文字符
                String responseBody = response.asString();
                if (responseBody.contains("JSON测试")) {
                    System.out.println("✅ 响应中正确包含中文字符");
                }
            }

            System.out.println("✅ 微服务间JSON数据传输测试完成");

        } catch (Exception e) {
            System.out.println("⚠️ 微服务间JSON数据传输测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试6：大数据量JSON序列化性能测试
     */
    @Test
    @Order(6)
    @DisplayName("6. 大数据量JSON序列化性能测试")
    void testLargeDataJsonPerformance() {
        System.out.println("测试6：大数据量JSON序列化性能测试");

        // 构造大量数据
        List<Map<String, Object>> largeDataList = java.util.stream.IntStream.range(0, 1000)
                .mapToObj(i -> Map.of(
                        "id", (long) i,
                        "name", "商品" + i,
                        "description", "这是第" + i + "个测试商品的详细描述信息",
                        "price", 99.99 + i,
                        "category", "分类" + (i % 10),
                        "tags", List.of("标签" + i, "测试", "性能"),
                        "metadata", Map.of(
                                "index", i,
                                "timestamp", System.currentTimeMillis(),
                                "random", UUID.randomUUID().toString()
                        )
                ))
                .toList();

        Map<String, Object> largeData = Map.of(
                "totalCount", largeDataList.size(),
                "data", largeDataList,
                "metadata", Map.of(
                        "testType", "PERFORMANCE",
                        "generatedAt", LocalDateTime.now().toString()
                )
        );

        try {
            long startTime = System.currentTimeMillis();

            // 测试大数据量序列化
            String jsonString = objectMapper.writeValueAsString(largeData);
            long serializationTime = System.currentTimeMillis() - startTime;

            System.out.println("大数据量序列化时间: " + serializationTime + "ms");
            System.out.println("序列化结果大小: " + jsonString.length() + " 字符");

            // 验证序列化性能（1000个对象应该在1秒内完成）
            assertTrue(serializationTime < 1000, 
                      "大数据量序列化时间应小于1000ms，实际: " + serializationTime + "ms");

            startTime = System.currentTimeMillis();

            // 测试大数据量反序列化
            Map<String, Object> deserializedData = objectMapper.readValue(jsonString, Map.class);
            long deserializationTime = System.currentTimeMillis() - startTime;

            System.out.println("大数据量反序列化时间: " + deserializationTime + "ms");

            // 验证反序列化性能
            assertTrue(deserializationTime < 1000, 
                      "大数据量反序列化时间应小于1000ms，实际: " + deserializationTime + "ms");

            // 验证数据完整性
            assertEquals(1000, deserializedData.get("totalCount"));

            System.out.println("✅ 大数据量JSON序列化性能测试成功");

        } catch (JsonProcessingException e) {
            fail("❌ 大数据量JSON序列化性能测试失败: " + e.getMessage());
        }
    }
}
