@echo off
echo ========================================
echo 跨服务集成测试执行脚本
echo ========================================

set SCRIPT_DIR=%~dp0
set PROJECT_ROOT=%SCRIPT_DIR%\..\..

echo 当前目录: %SCRIPT_DIR%
echo 项目根目录: %PROJECT_ROOT%

echo.
echo ========================================
echo 第一步：启动必要的微服务
echo ========================================

echo 启动 product-service (端口 8082)...
start "product-service" cmd /c "cd /d %PROJECT_ROOT%\microservices\product-service && mvn quarkus:dev"

echo 等待 product-service 启动...
timeout /t 15 /nobreak

echo 启动 order-service (端口 8084)...
start "order-service" cmd /c "cd /d %PROJECT_ROOT%\microservices\order-service && mvn quarkus:dev"

echo 等待 order-service 启动...
timeout /t 15 /nobreak

echo.
echo ========================================
echo 第二步：验证服务状态
echo ========================================

echo 检查 product-service 健康状态...
curl -s http://localhost:8082/health || echo product-service 未就绪

echo 检查 order-service 健康状态...
curl -s http://localhost:8084/health || echo order-service 未就绪

echo.
echo ========================================
echo 第三步：执行集成测试
echo ========================================

echo 编译集成测试模块...
cd /d %SCRIPT_DIR%
mvn clean compile

if %ERRORLEVEL% neq 0 (
    echo 集成测试编译失败！
    pause
    exit /b 1
)

echo.
echo 执行跨服务集成测试...
mvn test -Dtest=OrderProductIntegrationTest

if %ERRORLEVEL% neq 0 (
    echo 跨服务集成测试失败！
    pause
    exit /b 1
)

echo.
echo 执行Saga分布式事务测试...
mvn test -Dtest=SagaTransactionTest

if %ERRORLEVEL% neq 0 (
    echo Saga分布式事务测试失败！
    pause
    exit /b 1
)

echo.
echo ========================================
echo 第四步：生成测试报告
echo ========================================

echo 生成测试报告...
mvn surefire-report:report

echo.
echo ========================================
echo 集成测试完成！
echo ========================================
echo 测试报告位置: %SCRIPT_DIR%\target\site\surefire-report.html
echo.

pause
