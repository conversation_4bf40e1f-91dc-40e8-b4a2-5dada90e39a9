# Platform Integration Service 配置
quarkus:
  application:
    name: platform-integration
    version: 2.0.0

  # HTTP 服务配置
  http:
    port: 8085
    cors:
      ~: true
      origins: "*"
      methods: "*"
      headers: "*"

  # 数据库配置
  datasource:
    db-kind: postgresql
    username: ${DB_USERNAME:visthink}
    password: ${DB_PASSWORD:zylp}
    reactive:
      url: ${DB_URL:postgresql://*************:35432/platform_integration}
      max-size: 20
      idle-timeout: PT10M

  # Hibernate 配置
  hibernate-orm:
    database:
      generation: update
    log:
      sql: ${LOG_SQL:false}
      format-sql: true
      highlight-sql: true

  # Flyway 数据库迁移
  flyway:
    migrate-at-start: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    baseline-version: 1.0.0

  # Redis 配置
  redis:
    hosts: ${REDIS_URL:redis://localhost:6379}
    timeout: 10s
    max-pool-size: 20
    max-pool-waiting: 50

  # 日志配置
  log:
    level: INFO
    category:
      "com.visthink": DEBUG
      "org.hibernate.SQL": ${LOG_SQL:false}
    console:
      enable: true
      format: "%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [%c{3.}] (%t) %s%e%n"

  # OpenAPI 文档配置
  smallrye-openapi:
    info-title: Platform Integration Service API
    info-version: 2.0.0
    info-description: 电商平台集成服务API文档
    info-contact-name: VIS Think Team
    info-contact-email: <EMAIL>

  # 健康检查配置
  smallrye-health:
    root-path: /health

  # 监控配置
  micrometer:
    binder:
      http-server:
        enabled: true
      jvm:
        enabled: true
      system:
        enabled: true
    export:
      prometheus:
        enabled: true
        path: /metrics

# 调度器配置
quarkus.scheduler:
  enabled: true
  start-mode: normal

# 平台集成配置
platform:
  # 淘宝/天猫配置
  taobao:
    app-key: ${TAOBAO_APP_KEY:your_app_key}
    app-secret: ${TAOBAO_APP_SECRET:your_app_secret}
    gateway-url: https://eco.taobao.com/router/rest
    sandbox-url: https://gw.api.tbsandbox.com/router/rest
    sandbox-mode: ${TAOBAO_SANDBOX:true}
    timeout: 30s
    retry-count: 3

  # 拼多多配置
  pdd:
    client-id: ${PDD_CLIENT_ID:your_client_id}
    client-secret: ${PDD_CLIENT_SECRET:your_client_secret}
    gateway-url: https://gw-api.pinduoduo.com/api/router
    sandbox-url: https://open-api.pinduoduo.com/api/router
    sandbox-mode: ${PDD_SANDBOX:true}
    timeout: 30s
    retry-count: 3

  # 抖音配置
  douyin:
    app-id: ${DOUYIN_APP_ID:your_app_id}
    app-secret: ${DOUYIN_APP_SECRET:your_app_secret}
    gateway-url: https://openapi-fxg.jinritemai.com
    sandbox-url: https://openapi-sandbox.jinritemai.com
    sandbox-mode: ${DOUYIN_SANDBOX:true}
    timeout: 30s
    retry-count: 3

  # 京东配置
  jd:
    app-key: ${JD_APP_KEY:your_app_key}
    app-secret: ${JD_APP_SECRET:your_app_secret}
    gateway-url: https://api.jd.com/routerjson
    sandbox-url: https://api.jd.com/routerjson
    sandbox-mode: ${JD_SANDBOX:true}
    timeout: 30s
    retry-count: 3

# 同步配置
sync:
  # 商品同步配置
  product:
    enabled: true
    batch-size: 100
    interval: 5m
    retry-count: 3
    retry-delay: 30s

  # 订单同步配置
  order:
    enabled: true
    batch-size: 50
    interval: 2m
    retry-count: 3
    retry-delay: 30s

  # 库存同步配置
  inventory:
    enabled: true
    batch-size: 200
    interval: 10m
    retry-count: 3
    retry-delay: 30s

# 服务间通信配置
quarkus.rest-client:
  # 商品服务
  product-service:
    url: ${PRODUCT_SERVICE_URL:http://localhost:8082}
    scope: jakarta.inject.Singleton
    connect-timeout: 5000
    read-timeout: 30000

  # 库存服务
  inventory-service:
    url: ${INVENTORY_SERVICE_URL:http://localhost:8083}
    scope: jakarta.inject.Singleton
    connect-timeout: 5000
    read-timeout: 30000

  # 订单服务
  order-service:
    url: ${ORDER_SERVICE_URL:http://localhost:8084}
    scope: jakarta.inject.Singleton
    connect-timeout: 5000
    read-timeout: 30000

# 开发环境配置
"%dev":
  quarkus:
    log:
      level: DEBUG
      category:
        "com.visthink": DEBUG
    datasource:
      reactive:
        url: postgresql://localhost:5432/platform_integration_dev
    redis:
      hosts: redis://localhost:6379/1

# 测试环境配置
"%test":
  quarkus:
    datasource:
      db-kind: h2
      username: sa
      password:
      reactive:
        url: h2:mem:test;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    flyway:
      migrate-at-start: false
    redis:
      hosts: redis://localhost:6379/2

# 生产环境配置
"%prod":
  quarkus:
    log:
      level: INFO
      category:
        "com.visthink": INFO
    datasource:
      reactive:
        url: ${DB_URL}
        max-size: 50
    redis:
      hosts: ${REDIS_URL}
      max-pool-size: 50
