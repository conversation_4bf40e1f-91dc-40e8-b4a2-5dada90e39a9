# ===================================================================
# VisThink ERP - Workflow Service 配置文件
# 工作流引擎微服务配置
# ===================================================================

# 服务基础配置
quarkus:
  application:
    name: workflow-service
    version: 1.0.0
  
  # HTTP配置
  http:
    port: 8088
    host: 0.0.0.0
    cors:
      ~: true
      origins: "*"
      methods: "GET,POST,PUT,DELETE,OPTIONS"
      headers: "accept,authorization,content-type,x-requested-with,x-tenant-id"
    
  # 数据源配置
  datasource:
    db-kind: postgresql
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:zylp}
    reactive:
      url: postgresql://${DB_HOST:localhost}:${DB_PORT:35432}/${DB_NAME:workflow_db}
      max-size: 20
      idle-timeout: PT10M
    
  # Hibernate配置
  hibernate-orm:
    database:
      generation: none
    log:
      sql: ${LOG_SQL:false}
      format-sql: true
    
  # Flyway数据库迁移
  flyway:
    migrate-at-start: true
    baseline-on-migrate: true
    locations: classpath:db/migration
    
  # Redis配置
  redis:
    hosts: redis://${REDIS_HOST:localhost}:${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: ${REDIS_DB:2}
    timeout: 10s
    
  # JWT配置
  smallrye-jwt:
    enabled: true
    
  # OpenAPI配置
  smallrye-openapi:
    info-title: VisThink ERP - Workflow Service API
    info-version: 1.0.0
    info-description: 工作流引擎微服务API文档
    info-contact-name: VisThink Team
    info-contact-email: <EMAIL>
    
  # 健康检查
  smallrye-health:
    root-path: /health
    
  # 指标监控
  micrometer:
    export:
      prometheus:
        enabled: true
        path: /metrics
        
  # 日志配置
  log:
    level:
      ROOT: INFO
      com.visthink: DEBUG
      org.hibernate.SQL: ${LOG_SQL:false}
    console:
      enable: true
      format: "%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [%c{3.}] (%t) %s%e%n"
    file:
      enable: true
      path: logs/workflow-service.log
      format: "%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [%c{3.}] (%t) %s%e%n"
      rotation:
        max-file-size: 100M
        max-backup-index: 10
        
  # 调度器配置
  scheduler:
    enabled: true
    
  # 邮件配置
  mailer:
    from: ${MAIL_FROM:<EMAIL>}
    host: ${MAIL_HOST:smtp.qq.com}
    port: ${MAIL_PORT:587}
    start-tls: REQUIRED
    username: ${MAIL_USERNAME:}
    password: ${MAIL_PASSWORD:}

# 工作流引擎配置
workflow:
  # 引擎配置
  engine:
    # 任务执行器线程池配置
    executor:
      core-pool-size: 10
      max-pool-size: 50
      queue-capacity: 1000
      keep-alive-seconds: 60
      thread-name-prefix: "workflow-executor-"
    
    # 异步任务配置
    async:
      enabled: true
      timeout: 300000  # 5分钟超时
      
    # 缓存配置
    cache:
      enabled: true
      ttl: 3600  # 1小时过期
      max-size: 10000
      
  # 任务调度配置
  scheduler:
    # 超时检查任务
    timeout-check:
      enabled: true
      cron: "0 */5 * * * ?"  # 每5分钟执行一次
      
    # 提醒任务
    reminder:
      enabled: true
      cron: "0 */10 * * * ?"  # 每10分钟执行一次
      
    # 清理任务
    cleanup:
      enabled: true
      cron: "0 0 2 * * ?"  # 每天凌晨2点执行
      retention-days: 90  # 保留90天数据
      
  # 通知配置
  notification:
    # 邮件通知
    email:
      enabled: ${NOTIFICATION_EMAIL_ENABLED:true}
      template-path: templates/email
      
    # 短信通知
    sms:
      enabled: ${NOTIFICATION_SMS_ENABLED:false}
      provider: ${SMS_PROVIDER:aliyun}
      
    # 系统通知
    system:
      enabled: true
      
  # 表单配置
  form:
    # 文件上传配置
    upload:
      enabled: true
      max-file-size: 10MB
      allowed-types: "jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx"
      storage-path: ${FILE_STORAGE_PATH:/tmp/workflow/uploads}
      
  # 安全配置
  security:
    # 权限检查
    permission-check:
      enabled: true
      cache-enabled: true
      
    # 数据权限
    data-permission:
      enabled: true
      
    # 审计日志
    audit:
      enabled: true
      
  # 集成配置
  integration:
    # 用户服务集成
    member-service:
      base-url: ${MEMBER_SERVICE_URL:http://localhost:8081}
      timeout: 30s
      
    # 文件服务集成
    file-service:
      base-url: ${FILE_SERVICE_URL:http://localhost:8086}
      timeout: 60s

# 开发环境配置
"%dev":
  quarkus:
    log:
      level:
        com.visthink: DEBUG
        org.hibernate.SQL: true
    datasource:
      reactive:
        url: postgresql://localhost:35432/workflow_db
    redis:
      hosts: redis://localhost:6379
      
# 测试环境配置
"%test":
  quarkus:
    datasource:
      db-kind: h2
      username: sa
      password: 
      reactive:
        url: h2:mem:test;DB_CLOSE_DELAY=-1
    hibernate-orm:
      database:
        generation: drop-and-create
    flyway:
      migrate-at-start: false
      
# 生产环境配置
"%prod":
  quarkus:
    log:
      level:
        ROOT: WARN
        com.visthink: INFO
      file:
        enable: true
        path: /app/logs/workflow-service.log
    datasource:
      reactive:
        max-size: 50
        idle-timeout: PT30M
