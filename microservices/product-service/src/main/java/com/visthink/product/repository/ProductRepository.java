package com.visthink.product.repository;

import com.visthink.product.entity.Product;
import com.visthink.common.repository.BaseRepository;
import io.quarkus.panache.common.Page;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.List;

/**
 * 商品数据访问层
 * 继承BaseRepository获得通用CRUD功能，支持多租户数据隔离
 *
 * <AUTHOR>
 */
@ApplicationScoped
public class ProductRepository implements BaseRepository<Product> {

    /**
     * 根据租户ID分页查询商品
     *
     * @param tenantId 租户ID
     * @param page 分页参数
     * @return 商品列表
     */
    public Uni<List<Product>> findByTenantId(Long tenantId, Page page) {
        return find("tenantId = ?1 and deleted = 0 order by id desc", tenantId)
                .page(page)
                .list();
    }

    /**
     * 根据租户ID和商品编码查询
     *
     * @param tenantId 租户ID
     * @param productCode 商品编码
     * @return 商品对象
     */
    public Uni<Product> findByTenantAndCode(Long tenantId, String productCode) {
        return find("tenantId = ?1 and productCode = ?2 and deleted = 0", tenantId, productCode).firstResult();
    }

    /**
     * 检查商品编码是否存在
     *
     * @param tenantId 租户ID
     * @param productCode 商品编码
     * @return 是否存在
     */
    public Uni<Boolean> existsByTenantAndCode(Long tenantId, String productCode) {
        return find("tenantId = ?1 and productCode = ?2 and deleted = 0", tenantId, productCode)
                .count()
                .map(count -> count > 0);
    }

    /**
     * 根据状态查询商品
     *
     * @param tenantId 租户ID
     * @param status 商品状态
     * @param page 分页参数
     * @return 商品列表
     */
    public Uni<List<Product>> findByTenantAndStatus(Long tenantId, Integer status, Page page) {
        return find("tenantId = ?1 and status = ?2 and deleted = 0 order by id desc", tenantId, status)
                .page(page)
                .list();
    }

    /**
     * 根据分类查询商品
     *
     * @param tenantId 租户ID
     * @param categoryId 分类ID
     * @param page 分页参数
     * @return 商品列表
     */
    public Uni<List<Product>> findByTenantAndCategory(Long tenantId, Long categoryId, Page page) {
        return find("tenantId = ?1 and categoryId = ?2 and deleted = 0 order by id desc", tenantId, categoryId)
                .page(page)
                .list();
    }

    /**
     * 搜索商品
     *
     * @param tenantId 租户ID
     * @param keyword 搜索关键词
     * @param page 分页参数
     * @return 商品列表
     */
    public Uni<List<Product>> searchProducts(Long tenantId, String keyword, Page page) {
        String query = "tenantId = ?1 and (productName like ?2 or productCode like ?2 or keywords like ?2) and deleted = 0 order by id desc";
        String searchKeyword = "%" + keyword + "%";
        return find(query, tenantId, searchKeyword)
                .page(page)
                .list();
    }

    /**
     * 统计商品总数
     *
     * @param tenantId 租户ID
     * @return 商品总数
     */
    public Uni<Long> countByTenant(Long tenantId) {
        return count("tenantId = ?1 and deleted = 0", tenantId);
    }

    /**
     * 统计指定状态的商品数量
     *
     * @param tenantId 租户ID
     * @param status 商品状态
     * @return 商品数量
     */
    public Uni<Long> countByTenantAndStatus(Long tenantId, Integer status) {
        return count("tenantId = ?1 and status = ?2 and deleted = 0", tenantId, status);
    }

    /**
     * 根据ID列表查询商品
     *
     * @param tenantId 租户ID
     * @param productIds 商品ID列表
     * @return 商品列表
     */
    public Uni<List<Product>> findByTenantAndIds(Long tenantId, List<Long> productIds) {
        return find("tenantId = ?1 and id in ?2 and deleted = 0", tenantId, productIds).list();
    }

    /**
     * 批量更新商品状态
     *
     * @param tenantId 租户ID
     * @param productIds 商品ID列表
     * @param status 新状态
     * @return 更新数量
     */
    public Uni<Integer> updateStatusByIds(Long tenantId, List<Long> productIds, Integer status) {
        return update("status = ?1, updateTime = current_timestamp where tenantId = ?2 and id in ?3 and deleted = 0",
                status, tenantId, productIds);
    }

    /**
     * 获取热销商品
     *
     * @param tenantId 租户ID
     * @param page 分页参数
     * @return 热销商品列表
     */
    public Uni<List<Product>> findHotProducts(Long tenantId, Page page) {
        return find("tenantId = ?1 and isHot = true and status = 1 and deleted = 0 order by salesCount desc, id desc", tenantId)
                .page(page)
                .list();
    }

    /**
     * 获取推荐商品
     *
     * @param tenantId 租户ID
     * @param page 分页参数
     * @return 推荐商品列表
     */
    public Uni<List<Product>> findRecommendProducts(Long tenantId, Page page) {
        return find("tenantId = ?1 and isRecommend = true and status = 1 and deleted = 0 order by sortOrder desc, id desc", tenantId)
                .page(page)
                .list();
    }

    /**
     * 获取新品
     *
     * @param tenantId 租户ID
     * @param page 分页参数
     * @return 新品列表
     */
    public Uni<List<Product>> findNewProducts(Long tenantId, Page page) {
        return find("tenantId = ?1 and isNew = true and status = 1 and deleted = 0 order by createdAt desc", tenantId)
                .page(page)
                .list();
    }

    /**
     * 根据价格区间查询商品
     *
     * @param tenantId 租户ID
     * @param minPrice 最低价格
     * @param maxPrice 最高价格
     * @param page 分页参数
     * @return 商品列表
     */
    public Uni<List<Product>> findByPriceRange(Long tenantId, Double minPrice, Double maxPrice, Page page) {
        return find("tenantId = ?1 and salePrice >= ?2 and salePrice <= ?3 and status = 1 and deleted = 0 order by salePrice asc",
                tenantId, minPrice, maxPrice)
                .page(page)
                .list();
    }

    /**
     * 更新商品浏览量
     *
     * @param productId 商品ID
     * @return 更新数量
     */
    public Uni<Integer> incrementViewCount(Long productId) {
        return update("viewCount = viewCount + 1, updateTime = current_timestamp where id = ?1 and deleted = 0", productId);
    }

    /**
     * 更新商品销量
     *
     * @param productId 商品ID
     * @param quantity 销量增量
     * @return 更新数量
     */
    public Uni<Integer> incrementSalesCount(Long productId, Integer quantity) {
        return update("salesCount = salesCount + ?1, updateTime = current_timestamp where id = ?2 and deleted = 0",
                quantity, productId);
    }
}
