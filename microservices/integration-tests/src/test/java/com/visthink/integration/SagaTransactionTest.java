package com.visthink.integration;

import io.quarkus.test.junit.QuarkusTest;
import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.junit.jupiter.api.*;


import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static io.restassured.RestAssured.given;
import static org.hamcrest.Matchers.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.awaitility.Awaitility.await;

/**
 * Saga模式分布式事务集成测试
 *
 * 测试场景：
 * 1. 订单创建成功场景（正向流程）
 * 2. 库存不足回滚场景（补偿流程）
 * 3. 商品不存在回滚场景
 * 4. 支付失败回滚场景
 * 5. 分布式事务一致性验证
 *
 * <AUTHOR>
 */
@QuarkusTest
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@DisplayName("Saga分布式事务测试")
public class SagaTransactionTest {

    // 使用硬编码配置，避免配置注入问题
    private final Long defaultTenantId = 1L;
    private final String orderServiceUrl = "http://localhost:8084";
    private final String productServiceUrl = "http://localhost:8082";
    private final String inventoryServiceUrl = "http://localhost:8083";

    private static final String TENANT_HEADER = "X-Tenant-Id";

    @BeforeAll
    static void setupClass() {
        System.out.println("=== 开始Saga分布式事务测试 ===");
    }

    @AfterAll
    static void tearDownClass() {
        System.out.println("=== Saga分布式事务测试完成 ===");
    }

    @BeforeEach
    void setup() {
        // 等待所有相关服务启动完成
        waitForServiceReady(orderServiceUrl, "/health");
        waitForServiceReady(productServiceUrl, "/health");
        // 注意：inventory-service可能还未实现，这里先注释
        // waitForServiceReady(inventoryServiceUrl, "/health");
    }

    /**
     * 测试1：正向流程 - 订单创建成功
     */
    @Test
    @Order(1)
    @DisplayName("1. Saga正向流程 - 订单创建成功")
    void testSagaSuccessFlow() {
        System.out.println("测试1：Saga正向流程 - 订单创建成功");

        String businessId = "SAGA_TEST_" + UUID.randomUUID().toString().substring(0, 8);

        // 构造订单创建请求
        Map<String, Object> orderRequest = Map.of(
                "businessId", businessId,
                "customerId", 1L,
                "orderItems", List.of(
                        Map.of(
                                "productId", 1L,
                                "quantity", 1,
                                "unitPrice", 99.99
                        )
                ),
                "totalAmount", 99.99,
                "remark", "Saga正向流程测试"
        );

        // 步骤1：验证商品存在且可售
        Response productValidation = given()
                .header(TENANT_HEADER, defaultTenantId)
                .when()
                .get(productServiceUrl + "/api/products/1/validate")
                .then()
                .statusCode(200)
                .body("success", equalTo(true))
                .extract().response();

        System.out.println("商品验证结果: " + productValidation.asString());

        // 步骤2：获取商品价格信息
        Response priceInfo = given()
                .header(TENANT_HEADER, defaultTenantId)
                .queryParam("quantity", 1)
                .when()
                .get(productServiceUrl + "/api/products/1/price")
                .then()
                .statusCode(200)
                .body("success", equalTo(true))
                .extract().response();

        System.out.println("商品价格信息: " + priceInfo.asString());

        // 步骤3：创建订单（模拟Saga协调器）
        Response orderCreation = given()
                .header(TENANT_HEADER, defaultTenantId)
                .contentType(ContentType.JSON)
                .body(orderRequest)
                .when()
                .post(orderServiceUrl + "/api/orders")
                .then()
                .statusCode(anyOf(is(200), is(201)))
                .extract().response();

        System.out.println("订单创建结果: " + orderCreation.asString());

        // 验证Saga事务成功
        assertTrue(orderCreation.jsonPath().getBoolean("success"), "Saga事务应该成功");
    }

    /**
     * 测试2：补偿流程 - 商品不存在场景
     */
    @Test
    @Order(2)
    @DisplayName("2. Saga补偿流程 - 商品不存在场景")
    void testSagaCompensationFlow_ProductNotFound() {
        System.out.println("测试2：Saga补偿流程 - 商品不存在场景");

        String businessId = "SAGA_COMP_" + UUID.randomUUID().toString().substring(0, 8);
        Long nonExistentProductId = 99999L;

        // 构造包含不存在商品的订单请求
        Map<String, Object> orderRequest = Map.of(
                "businessId", businessId,
                "customerId", 1L,
                "orderItems", List.of(
                        Map.of(
                                "productId", nonExistentProductId,
                                "quantity", 1,
                                "unitPrice", 99.99
                        )
                ),
                "totalAmount", 99.99,
                "remark", "Saga补偿流程测试"
        );

        // 步骤1：验证商品（应该失败）
        Response productValidation = given()
                .header(TENANT_HEADER, defaultTenantId)
                .when()
                .get(productServiceUrl + "/api/products/" + nonExistentProductId + "/validate")
                .then()
                .statusCode(200)
                .extract().response();

        System.out.println("商品验证结果: " + productValidation.asString());

        // 验证商品验证失败
        if (productValidation.jsonPath().getBoolean("success")) {
            Boolean isValid = productValidation.jsonPath().getBoolean("data");
            assertFalse(isValid, "不存在的商品验证应该失败");
        }

        // 步骤2：尝试创建订单（应该失败或触发补偿）
        Response orderCreation = given()
                .header(TENANT_HEADER, defaultTenantId)
                .contentType(ContentType.JSON)
                .body(orderRequest)
                .when()
                .post(orderServiceUrl + "/api/orders")
                .then()
                .statusCode(anyOf(is(200), is(400), is(422))) // 允许多种错误状态码
                .extract().response();

        System.out.println("订单创建结果: " + orderCreation.asString());

        // 验证Saga补偿流程
        if (orderCreation.getStatusCode() == 200) {
            // 如果返回200，检查业务逻辑是否正确处理了错误
            assertFalse(orderCreation.jsonPath().getBoolean("success"), "包含无效商品的订单创建应该失败");
        }
    }

    /**
     * 测试3：并发Saga事务测试
     */
    @Test
    @Order(3)
    @DisplayName("3. 并发Saga事务测试")
    void testConcurrentSagaTransactions() {
        System.out.println("测试3：并发Saga事务测试");

        int concurrentTransactions = 5;

        // 并发创建多个订单
        List<Response> responses = java.util.stream.IntStream.range(0, concurrentTransactions)
                .parallel()
                .mapToObj(i -> {
                    String businessId = "SAGA_CONCURRENT_" + i + "_" + UUID.randomUUID().toString().substring(0, 8);

                    Map<String, Object> orderRequest = Map.of(
                            "businessId", businessId,
                            "customerId", 1L,
                            "orderItems", List.of(
                                    Map.of(
                                            "productId", 1L,
                                            "quantity", 1,
                                            "unitPrice", 99.99
                                    )
                            ),
                            "totalAmount", 99.99,
                            "remark", "并发Saga测试_" + i
                    );

                    return given()
                            .header(TENANT_HEADER, defaultTenantId)
                            .contentType(ContentType.JSON)
                            .body(orderRequest)
                            .when()
                            .post(orderServiceUrl + "/api/orders");
                })
                .toList();

        // 验证所有并发事务的结果
        responses.forEach(response -> {
            System.out.println("并发事务响应状态: " + response.getStatusCode());
            System.out.println("并发事务响应内容: " + response.asString());

            // 验证响应状态
            assertTrue(response.getStatusCode() >= 200 && response.getStatusCode() < 500,
                    "并发Saga事务应该有合理的响应状态");
        });
    }

    /**
     * 测试4：Saga事务超时处理
     */
    @Test
    @Order(4)
    @DisplayName("4. Saga事务超时处理测试")
    void testSagaTransactionTimeout() {
        System.out.println("测试4：Saga事务超时处理测试");

        String businessId = "SAGA_TIMEOUT_" + UUID.randomUUID().toString().substring(0, 8);

        // 构造订单创建请求
        Map<String, Object> orderRequest = Map.of(
                "businessId", businessId,
                "customerId", 1L,
                "orderItems", List.of(
                        Map.of(
                                "productId", 1L,
                                "quantity", 1,
                                "unitPrice", 99.99
                        )
                ),
                "totalAmount", 99.99,
                "remark", "Saga超时测试",
                "timeout", 1000 // 设置较短的超时时间（如果支持）
        );

        long startTime = System.currentTimeMillis();

        Response response = given()
                .header(TENANT_HEADER, defaultTenantId)
                .contentType(ContentType.JSON)
                .body(orderRequest)
                .when()
                .post(orderServiceUrl + "/api/orders")
                .then()
                .statusCode(anyOf(is(200), is(201), is(408), is(500))) // 允许超时相关状态码
                .extract().response();

        long responseTime = System.currentTimeMillis() - startTime;

        System.out.println("Saga超时测试响应: " + response.asString());
        System.out.println("响应时间: " + responseTime + "ms");

        // 验证超时处理
        if (response.getStatusCode() == 408 || response.getStatusCode() == 500) {
            System.out.println("Saga事务超时处理正常");
        }
    }

    /**
     * 工具方法：等待服务就绪
     */
    private void waitForServiceReady(String serviceUrl, String healthPath) {
        await()
                .atMost(Duration.ofSeconds(30))
                .pollInterval(Duration.ofSeconds(2))
                .until(() -> {
                    try {
                        Response response = given()
                                .when()
                                .get(serviceUrl + healthPath);
                        return response.getStatusCode() == 200;
                    } catch (Exception e) {
                        System.out.println("等待服务就绪: " + serviceUrl + " - " + e.getMessage());
                        return false;
                    }
                });
    }
}
