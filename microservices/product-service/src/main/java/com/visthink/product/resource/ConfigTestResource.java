package com.visthink.product.resource;

import io.smallrye.mutiny.Uni;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import jakarta.inject.Inject;
import java.util.HashMap;
import java.util.Map;

/**
 * 配置测试资源 - 用于调试数据库连接配置
 * 
 * <AUTHOR>
 */
@Path("/api/config-test")
@Produces(MediaType.APPLICATION_JSON)
public class ConfigTestResource {

    @ConfigProperty(name = "quarkus.datasource.reactive.url", defaultValue = "未配置")
    String datasourceUrl;

    @ConfigProperty(name = "quarkus.datasource.username", defaultValue = "未配置")
    String datasourceUsername;

    @ConfigProperty(name = "quarkus.datasource.reactive.username", defaultValue = "未配置")
    String reactiveDatasourceUsername;

    @ConfigProperty(name = "quarkus.datasource.password", defaultValue = "未配置")
    String datasourcePassword;

    @ConfigProperty(name = "quarkus.datasource.reactive.password", defaultValue = "未配置")
    String reactiveDatasourcePassword;

    /**
     * 获取数据库配置信息
     */
    @GET
    @Path("/datasource")
    public Uni<Map<String, String>> getDatasourceConfig() {
        Map<String, String> config = new HashMap<>();
        config.put("datasource.reactive.url", datasourceUrl);
        config.put("datasource.username", datasourceUsername);
        config.put("datasource.reactive.username", reactiveDatasourceUsername);
        config.put("datasource.password", "***"); // 隐藏密码
        config.put("datasource.reactive.password", "***"); // 隐藏密码
        
        return Uni.createFrom().item(config);
    }
}
