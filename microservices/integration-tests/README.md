# 集成测试框架

## 📋 概述

本模块提供了完整的微服务集成测试框架，用于验证Visthink ERP系统中各个微服务之间的协作和通信。

## 🎯 测试目标

### 第二优先级任务实施
- ✅ **完善集成测试框架** - 创建完整的跨服务集成测试
- ✅ **修复JSON序列化问题** - 解决微服务间通信的序列化问题  
- ✅ **优化API响应时间** - 确保API响应时间<500ms
- ✅ **验证跨服务通信** - 测试order-service与product-service的集成

## 🧪 测试类型

### 1. SimpleIntegrationTest - 简化集成测试
**测试场景：**
- 服务健康检查
- 商品服务API测试
- 订单服务API测试
- 跨服务集成场景测试
- JSON序列化验证
- API响应时间测试
- 并发性能测试
- 多租户数据隔离测试

### 2. JsonSerializationTest - JSON序列化测试
**测试场景：**
- 基础数据类型序列化测试
- 复杂对象序列化测试
- 日期时间序列化测试
- 中文字符序列化测试
- 微服务间JSON数据传输测试
- 大数据量JSON序列化性能测试

### 3. SagaTransactionTest - Saga分布式事务测试
**测试场景：**
- 订单创建成功场景（正向流程）
- 商品不存在回滚场景（补偿流程）
- 并发Saga事务测试
- Saga事务超时处理测试

### 4. PerformanceMonitoringTest - 性能监控测试
**测试场景：**
- API响应时间监控测试
- 并发负载测试
- 微服务健康状态监控测试
- 数据库连接性能测试
- 内存使用监控测试

## 🚀 快速开始

### 前置条件
1. Java 17+
2. Maven 3.8+
3. 微服务已启动（可选，测试会优雅处理服务未启动的情况）

### 运行测试

#### 方式1：使用脚本（推荐）
```bash
# Windows
run-tests.bat

# 选择测试类型：
# 1. 简化集成测试
# 2. JSON序列化测试  
# 3. Saga分布式事务测试
# 4. 性能监控测试
# 5. 执行所有测试
# 6. 仅执行健康检查测试
```

#### 方式2：使用Maven命令
```bash
# 编译测试
mvn clean compile test-compile

# 执行所有测试
mvn test

# 执行特定测试类
mvn test -Dtest=SimpleIntegrationTest
mvn test -Dtest=JsonSerializationTest
mvn test -Dtest=SagaTransactionTest
mvn test -Dtest=PerformanceMonitoringTest

# 执行特定测试方法
mvn test -Dtest=SimpleIntegrationTest#testServiceHealth
mvn test -Dtest=PerformanceMonitoringTest#testApiResponseTimeMonitoring

# 生成测试报告
mvn surefire-report:report
```

## 📊 性能指标

### 响应时间目标
- **API响应时间**: < 500ms
- **健康检查**: < 5秒
- **数据库查询**: < 500ms
- **JSON序列化**: < 100ms（复杂对象）

### 并发性能目标
- **并发用户数**: 10个用户
- **测试持续时间**: 30秒
- **成功率**: ≥ 80%
- **吞吐量**: ≥ 1请求/秒

### 测试覆盖率目标
- **集成测试覆盖率**: > 80%
- **API端点覆盖**: 主要业务API
- **错误场景覆盖**: 异常处理验证

## 🔧 配置说明

### 服务配置
```yaml
# application.yml
test:
  tenant:
    default-id: 1
    test-id: 999
  performance:
    max-response-time: 500  # API响应时间要求（毫秒）
    concurrent-threads: 10  # 并发测试线程数
    test-duration: 30       # 测试持续时间（秒）
```

### 服务端点
- **商品服务**: http://localhost:8082
- **订单服务**: http://localhost:8084
- **库存服务**: http://localhost:8083（可选）

## 📈 测试报告

### 报告位置
- **HTML报告**: `target/site/surefire-report.html`
- **XML报告**: `target/surefire-reports/`
- **控制台输出**: 实时测试结果

### 报告内容
- 测试执行统计
- 性能指标分析
- 错误详情
- 响应时间分布

## 🛠️ 故障排除

### 常见问题

#### 1. 服务连接失败
```
⚠️ 商品服务健康检查失败: Connection refused: connect
```
**解决方案**: 确保相关微服务已启动，或者测试会优雅处理此情况

#### 2. 响应时间超标
```
API响应时间应小于500ms，实际: 800ms
```
**解决方案**: 
- 检查数据库连接
- 优化查询语句
- 增加缓存

#### 3. JSON序列化错误
```
JSON序列化测试失败: JsonProcessingException
```
**解决方案**:
- 检查Jackson配置
- 验证DTO类注解
- 确认数据类型兼容性

### 调试模式
```bash
# 启用详细日志
mvn test -Dtest=SimpleIntegrationTest -X

# 启用REST Assured日志
mvn test -Dtest=SimpleIntegrationTest -Dio.restassured.debug=true
```

## 🔄 持续集成

### CI/CD集成
```yaml
# GitHub Actions / Jenkins 示例
- name: Run Integration Tests
  run: |
    cd microservices/integration-tests
    mvn clean test
    mvn surefire-report:report
```

### 测试策略
1. **冒烟测试**: 健康检查 + 基础API
2. **回归测试**: 完整集成测试套件
3. **性能测试**: 响应时间 + 并发负载
4. **压力测试**: 大数据量 + 长时间运行

## 📝 开发指南

### 添加新测试
1. 在相应的测试类中添加测试方法
2. 使用`@Order`注解控制执行顺序
3. 添加详细的中文注释
4. 验证响应时间和错误处理

### 测试最佳实践
- 使用有意义的测试名称
- 提供详细的错误信息
- 优雅处理服务不可用情况
- 验证多租户数据隔离
- 监控性能指标

## 📞 支持

如有问题，请联系：
- **开发团队**: <EMAIL>
- **文档**: 项目Wiki
- **问题跟踪**: GitHub Issues
