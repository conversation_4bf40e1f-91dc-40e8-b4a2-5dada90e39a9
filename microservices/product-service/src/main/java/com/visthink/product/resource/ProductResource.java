package com.visthink.product.resource;

import com.visthink.common.base.SimpleController;
import com.visthink.common.dto.ApiResponse;
import com.visthink.common.dto.PageResult;
import com.visthink.common.dto.RestResult;
import com.visthink.product.service.ProductService;
import com.visthink.product.dto.request.ProductCreateRequest;
import com.visthink.product.dto.request.ProductUpdateRequest;
import com.visthink.product.dto.query.ProductQueryRequest;
import com.visthink.product.dto.response.ProductResponse;
import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.parameters.Parameter;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import org.jboss.logging.Logger;

import java.util.List;
import java.util.Map;

/**
 * 商品管理REST接口 - 重构版本
 *
 * 重构说明：
 * 1. 继承SimpleController，只处理HTTP相关逻辑
 * 2. 所有业务逻辑委托给ProductService处理
 * 3. 职责清晰：Controller专注HTTP，Service专注业务
 * 4. 易于测试和维护
 *
 * <AUTHOR>
 */
@Path("/api/products")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "商品管理", description = "商品管理相关接口")
public class ProductResource extends SimpleController {

    private static final Logger log = Logger.getLogger(ProductResource.class);

    @Inject
    ProductService productService;

    @POST
    @Operation(summary = "创建商品", description = "创建新的商品信息")
    @APIResponse(responseCode = "200", description = "创建成功")
    public Uni<ApiResponse<ProductResponse>> createProduct(@Valid ProductCreateRequest request) {
        logOperation("创建商品", "开始处理创建请求");

        return getCurrentTenantId()
                .flatMap(tenantId -> {
                    logOperation("创建商品", tenantId, "租户验证通过");
                    return productService.createProduct(tenantId, request);
                })
                .map(product -> {
                    logOperation("创建商品", "创建成功，商品ID: " + product.getId());
                    return success("创建商品成功", product);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "创建商品"));
    }

    @PUT
    @Path("/{productId}")
    @Operation(summary = "更新商品", description = "更新商品信息")
    @APIResponse(responseCode = "200", description = "更新成功")
    public Uni<ApiResponse<ProductResponse>> updateProduct(
            @PathParam("productId") @Parameter(description = "商品ID") Long productId,
            @Valid ProductUpdateRequest request) {
        logOperation("更新商品", "商品ID: " + productId);

        return getCurrentTenantId()
                .flatMap(tenantId -> productService.updateProduct(tenantId, productId, request))
                .map(product -> {
                    logOperation("更新商品", "更新成功");
                    return success("更新商品成功", product);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "更新商品"));
    }

    @GET
    @Path("/{productId}")
    @Operation(summary = "获取商品详情", description = "根据ID获取商品详细信息")
    @APIResponse(responseCode = "200", description = "查询成功")
    public Uni<ApiResponse<ProductResponse>> getProductById(
            @PathParam("productId") @Parameter(description = "商品ID") Long productId) {
        logOperation("获取商品详情", "商品ID: " + productId);

        return getCurrentTenantId()
                .flatMap(tenantId -> productService.getProductById(tenantId, productId))
                .map(product -> {
                    if (product != null) {
                        logOperation("获取商品详情", "查询成功");
                        return success(product);
                    } else {
                        return ApiResponse.<ProductResponse>notFound("商品不存在");
                    }
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "获取商品详情"));
    }

    @GET
    @Path("/code/{productCode}")
    @Operation(summary = "根据编码获取商品", description = "根据商品编码获取商品详细信息")
    @APIResponse(responseCode = "200", description = "查询成功")
    public Uni<ApiResponse<ProductResponse>> getProductByCode(
            @PathParam("productCode") @Parameter(description = "商品编码") String productCode) {
        logOperation("根据编码获取商品", "商品编码: " + productCode);

        return getCurrentTenantId()
                .flatMap(tenantId -> productService.getProductByCode(tenantId, productCode))
                .map(product -> {
                    if (product != null) {
                        logOperation("根据编码获取商品", "查询成功");
                        return success(product);
                    } else {
                        return ApiResponse.<ProductResponse>notFound("商品不存在");
                    }
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "根据编码获取商品"));
    }

    @GET
    @Operation(summary = "分页查询商品", description = "分页查询商品列表")
    @APIResponse(responseCode = "200", description = "查询成功")
    public Uni<ApiResponse<PageResult<ProductResponse>>> getProductList(@BeanParam @Valid ProductQueryRequest request) {
        logOperation("分页查询商品", "开始处理查询请求");

        return getCurrentTenantId()
                .flatMap(tenantId -> productService.getProductList(tenantId, request))
                .map(pageResult -> {
                    logOperation("分页查询商品", "查询成功");
                    return success(pageResult);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "分页查询商品"));
    }

    /**
     * 删除商品
     */
    @DELETE
    @Path("/{productId}")
    @Operation(summary = "删除商品", description = "根据ID删除商品")
    @APIResponse(responseCode = "200", description = "删除成功")
    public Uni<ApiResponse<Boolean>> deleteProduct(@PathParam("productId") Long productId) {
        logOperation("删除商品", "商品ID: " + productId);

        return getCurrentTenantId()
                .flatMap(tenantId -> productService.deleteProduct(tenantId, productId))
                .map(result -> {
                    if (result) {
                        logOperation("删除商品", "删除成功");
                        return success("删除商品成功", true);
                    } else {
                        return ApiResponse.<Boolean>notFound("商品不存在");
                    }
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "删除商品"));
    }

    @PUT
    @Path("/batch/publish")
    @Operation(summary = "批量上架商品", description = "批量上架商品")
    public Uni<RestResult<Boolean>> batchPublishProducts(
            @HeaderParam("X-Tenant-Id") @Parameter(description = "租户ID") Long tenantId,
            @NotNull List<Long> productIds) {

        return productService.batchPublishProducts(tenantId, productIds)
                .map(RestResult::success)
                .onFailure().recoverWithItem(throwable -> {
                    log.error("批量上架商品失败", throwable);
                    return RestResult.error(throwable.getMessage());
                });
    }

    @PUT
    @Path("/batch/unpublish")
    @Operation(summary = "批量下架商品", description = "批量下架商品")
    public Uni<RestResult<Boolean>> batchUnpublishProducts(
            @HeaderParam("X-Tenant-Id") @Parameter(description = "租户ID") Long tenantId,
            @NotNull List<Long> productIds) {

        return productService.batchUnpublishProducts(tenantId, productIds)
                .map(RestResult::success)
                .onFailure().recoverWithItem(throwable -> {
                    log.error("批量下架商品失败", throwable);
                    return RestResult.error(throwable.getMessage());
                });
    }

    @PUT
    @Path("/{productId}/publish-all-platforms")
    @Operation(summary = "一键上架到所有平台", description = "将商品一键上架到所有已配置的平台")
    public Uni<RestResult<Boolean>> publishToAllPlatforms(
            @HeaderParam("X-Tenant-Id") @Parameter(description = "租户ID") Long tenantId,
            @PathParam("productId") @Parameter(description = "商品ID") Long productId) {

        return productService.publishToAllPlatforms(tenantId, productId)
                .map(RestResult::success)
                .onFailure().recoverWithItem(throwable -> {
                    log.error("一键上架到所有平台失败", throwable);
                    return RestResult.error(throwable.getMessage());
                });
    }

    @PUT
    @Path("/{productId}/publish-platform/{platformCode}")
    @Operation(summary = "上架到指定平台", description = "将商品上架到指定平台")
    public Uni<RestResult<Boolean>> publishToPlatform(
            @HeaderParam("X-Tenant-Id") @Parameter(description = "租户ID") Long tenantId,
            @PathParam("productId") @Parameter(description = "商品ID") Long productId,
            @PathParam("platformCode") @Parameter(description = "平台编码") String platformCode) {

        return productService.publishToPlatform(tenantId, productId, platformCode)
                .map(RestResult::success)
                .onFailure().recoverWithItem(throwable -> {
                    log.error("上架到指定平台失败", throwable);
                    return RestResult.error(throwable.getMessage());
                });
    }

    @PUT
    @Path("/{productId}/sync-platform/{platformCode}")
    @Operation(summary = "同步商品到平台", description = "同步商品信息到指定平台")
    public Uni<RestResult<Boolean>> syncProductToPlatform(
            @HeaderParam("X-Tenant-Id") @Parameter(description = "租户ID") Long tenantId,
            @PathParam("productId") @Parameter(description = "商品ID") Long productId,
            @PathParam("platformCode") @Parameter(description = "平台编码") String platformCode) {

        return productService.syncProductToPlatform(tenantId, productId, platformCode)
                .map(RestResult::success)
                .onFailure().recoverWithItem(throwable -> {
                    log.error("同步商品到平台失败", throwable);
                    return RestResult.error(throwable.getMessage());
                });
    }

    @GET
    @Path("/statistics")
    @Operation(summary = "获取商品统计", description = "获取商品统计信息")
    public Uni<RestResult<Map<String, Object>>> getProductStatistics(
            @HeaderParam("X-Tenant-Id") @Parameter(description = "租户ID") Long tenantId) {

        return productService.getProductStatistics(tenantId)
                .map(RestResult::success)
                .onFailure().recoverWithItem(throwable -> {
                    log.error("获取商品统计失败", throwable);
                    return RestResult.error(throwable.getMessage());
                });
    }

    @GET
    @Path("/hot")
    @Operation(summary = "获取热销商品", description = "获取热销商品列表")
    public Uni<RestResult<PageResult<ProductResponse>>> getHotProducts(
            @HeaderParam("X-Tenant-Id") @Parameter(description = "租户ID") Long tenantId,
            @QueryParam("page") @DefaultValue("1") @Parameter(description = "页码") int page,
            @QueryParam("size") @DefaultValue("20") @Parameter(description = "每页大小") int size) {

        return productService.getHotProducts(tenantId, page, size)
                .map(RestResult::success)
                .onFailure().recoverWithItem(throwable -> {
                    log.error("获取热销商品失败", throwable);
                    return RestResult.error(throwable.getMessage());
                });
    }

    @GET
    @Path("/recommend")
    @Operation(summary = "获取推荐商品", description = "获取推荐商品列表")
    public Uni<RestResult<PageResult<ProductResponse>>> getRecommendProducts(
            @HeaderParam("X-Tenant-Id") @Parameter(description = "租户ID") Long tenantId,
            @QueryParam("page") @DefaultValue("1") @Parameter(description = "页码") int page,
            @QueryParam("size") @DefaultValue("20") @Parameter(description = "每页大小") int size) {

        return productService.getRecommendProducts(tenantId, page, size)
                .map(RestResult::success)
                .onFailure().recoverWithItem(throwable -> {
                    log.error("获取推荐商品失败", throwable);
                    return RestResult.error(throwable.getMessage());
                });
    }

    @GET
    @Path("/new")
    @Operation(summary = "获取新品", description = "获取新品列表")
    public Uni<RestResult<PageResult<ProductResponse>>> getNewProducts(
            @HeaderParam("X-Tenant-Id") @Parameter(description = "租户ID") Long tenantId,
            @QueryParam("page") @DefaultValue("1") @Parameter(description = "页码") int page,
            @QueryParam("size") @DefaultValue("20") @Parameter(description = "每页大小") int size) {

        return productService.getNewProducts(tenantId, page, size)
                .map(RestResult::success)
                .onFailure().recoverWithItem(throwable -> {
                    log.error("获取新品失败", throwable);
                    return RestResult.error(throwable.getMessage());
                });
    }

    @GET
    @Path("/price-range")
    @Operation(summary = "按价格区间查询商品", description = "根据价格区间查询商品")
    public Uni<RestResult<PageResult<ProductResponse>>> getProductsByPriceRange(
            @HeaderParam("X-Tenant-Id") @Parameter(description = "租户ID") Long tenantId,
            @QueryParam("minPrice") @Parameter(description = "最低价格") Double minPrice,
            @QueryParam("maxPrice") @Parameter(description = "最高价格") Double maxPrice,
            @QueryParam("page") @DefaultValue("1") @Parameter(description = "页码") int page,
            @QueryParam("size") @DefaultValue("20") @Parameter(description = "每页大小") int size) {

        return productService.getProductsByPriceRange(tenantId, minPrice, maxPrice, page, size)
                .map(RestResult::success)
                .onFailure().recoverWithItem(throwable -> {
                    log.error("按价格区间查询商品失败", throwable);
                    return RestResult.error(throwable.getMessage());
                });
    }

    @PUT
    @Path("/{productId}/view")
    @Operation(summary = "增加浏览量", description = "增加商品浏览量")
    public Uni<RestResult<Boolean>> incrementViewCount(
            @PathParam("productId") @Parameter(description = "商品ID") Long productId) {

        return productService.incrementViewCount(productId)
                .map(RestResult::success)
                .onFailure().recoverWithItem(throwable -> {
                    log.error("增加浏览量失败", throwable);
                    return RestResult.error(throwable.getMessage());
                });
    }

    @GET
    @Path("/check-code/{productCode}")
    @Operation(summary = "检查商品编码", description = "检查商品编码是否已存在")
    public Uni<RestResult<Boolean>> checkProductCodeExists(
            @HeaderParam("X-Tenant-Id") @Parameter(description = "租户ID") Long tenantId,
            @PathParam("productCode") @Parameter(description = "商品编码") String productCode) {

        return productService.checkProductCodeExists(tenantId, productCode)
                .map(RestResult::success)
                .onFailure().recoverWithItem(throwable -> {
                    log.error("检查商品编码失败", throwable);
                    return RestResult.error(throwable.getMessage());
                });
    }

    @GET
    @Path("/category/{categoryId}")
    @Operation(summary = "按分类查询商品", description = "根据分类查询商品")
    public Uni<RestResult<PageResult<ProductResponse>>> getProductsByCategory(
            @HeaderParam("X-Tenant-Id") @Parameter(description = "租户ID") Long tenantId,
            @PathParam("categoryId") @Parameter(description = "分类ID") Long categoryId,
            @QueryParam("page") @DefaultValue("1") @Parameter(description = "页码") int page,
            @QueryParam("size") @DefaultValue("20") @Parameter(description = "每页大小") int size) {

        return productService.getProductsByCategory(tenantId, categoryId, page, size)
                .map(RestResult::success)
                .onFailure().recoverWithItem(throwable -> {
                    log.error("按分类查询商品失败", throwable);
                    return RestResult.error(throwable.getMessage());
                });
    }

    // ==================== 跨服务集成API ====================

    /**
     * 验证商品是否存在且可售（供订单服务调用）
     */
    @GET
    @Path("/{productId}/validate")
    @Operation(summary = "验证商品", description = "验证商品是否存在且可售")
    @APIResponse(responseCode = "200", description = "验证成功")
    public Uni<ApiResponse<Boolean>> validateProduct(
            @PathParam("productId") @Parameter(description = "商品ID") Long productId) {
        logOperation("验证商品", "商品ID: " + productId);

        return getCurrentTenantId()
                .flatMap(tenantId -> productService.getProductById(tenantId, productId))
                .map(product -> {
                    if (product != null && product.getStatus() == 1) { // 1表示正常状态
                        logOperation("验证商品", "商品验证通过");
                        return success("商品验证通过", true);
                    } else {
                        logOperation("验证商品", "商品不存在或不可售");
                        return success("商品不存在或不可售", false);
                    }
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "验证商品"));
    }

    /**
     * 获取商品价格信息（供订单服务调用）
     */
    @GET
    @Path("/{productId}/price")
    @Operation(summary = "获取商品价格", description = "获取商品价格信息")
    @APIResponse(responseCode = "200", description = "查询成功")
    public Uni<ApiResponse<ProductPriceInfo>> getProductPrice(
            @PathParam("productId") @Parameter(description = "商品ID") Long productId,
            @QueryParam("quantity") @Parameter(description = "数量") Integer quantity) {
        logOperation("获取商品价格", "商品ID: " + productId + ", 数量: " + quantity);

        return getCurrentTenantId()
                .flatMap(tenantId -> productService.getProductById(tenantId, productId))
                .map(product -> {
                    if (product != null) {
                        ProductPriceInfo priceInfo = new ProductPriceInfo();
                        priceInfo.setProductId(productId);
                        priceInfo.setUnitPrice(product.getSalePrice());

                        // 计算总价
                        if (quantity != null && quantity > 0) {
                            priceInfo.setTotalPrice(product.getSalePrice().multiply(new java.math.BigDecimal(quantity)));
                        } else {
                            priceInfo.setTotalPrice(product.getSalePrice());
                        }

                        priceInfo.setDiscountAmount(java.math.BigDecimal.ZERO);
                        priceInfo.setPriceType("NORMAL");

                        logOperation("获取商品价格", "查询成功");
                        return success(priceInfo);
                    } else {
                        return ApiResponse.<ProductPriceInfo>notFound("商品不存在");
                    }
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "获取商品价格"));
    }

    /**
     * 批量获取商品信息（供订单服务调用）
     */
    @POST
    @Path("/batch")
    @Operation(summary = "批量获取商品", description = "批量获取商品信息")
    @APIResponse(responseCode = "200", description = "查询成功")
    public Uni<ApiResponse<List<ProductResponse>>> getBatchProducts(List<Long> productIds) {
        logOperation("批量获取商品", "商品ID数量: " + (productIds != null ? productIds.size() : 0));

        if (productIds == null || productIds.isEmpty()) {
            return Uni.createFrom().item(success("批量获取商品成功", List.of()));
        }

        return getCurrentTenantId()
                .flatMap(tenantId -> {
                    // 使用并行查询提高性能
                    List<Uni<ProductResponse>> productUnis = productIds.stream()
                            .map(productId -> productService.getProductById(tenantId, productId)
                                    .onFailure().recoverWithItem((ProductResponse) null))
                            .toList();

                    return Uni.combine().all().unis(productUnis)
                            .with(results -> {
                                List<ProductResponse> products = results.stream()
                                        .filter(product -> product != null)
                                        .map(product -> (ProductResponse) product)
                                        .toList();
                                return products;
                            });
                })
                .map(products -> {
                    logOperation("批量获取商品", "查询成功，返回商品数量: " + products.size());
                    return success("批量获取商品成功", products);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "批量获取商品"));
    }

    /**
     * 根据SKU ID获取商品信息（供订单服务调用）
     */
    @GET
    @Path("/sku/{skuId}")
    @Operation(summary = "根据SKU获取商品", description = "根据SKU ID获取商品信息")
    @APIResponse(responseCode = "200", description = "查询成功")
    public Uni<ApiResponse<ProductResponse>> getProductBySkuId(
            @PathParam("skuId") @Parameter(description = "SKU ID") Long skuId) {
        logOperation("根据SKU获取商品", "SKU ID: " + skuId);

        return getCurrentTenantId()
                .flatMap(tenantId -> {
                    // TODO: 实现根据SKU ID查询商品的逻辑
                    // 这里需要先查询SKU，然后根据SKU的productId查询商品
                    return Uni.createFrom().item(ApiResponse.<ProductResponse>error("根据SKU查询商品功能待实现"));
                })
                .onFailure().recoverWithItem(throwable -> {
                    log.error("根据SKU获取商品失败", throwable);
                    return ApiResponse.<ProductResponse>error(throwable.getMessage());
                });
    }

    /**
     * 商品价格信息DTO
     */
    public static class ProductPriceInfo {
        private Long productId;
        private java.math.BigDecimal unitPrice;
        private java.math.BigDecimal totalPrice;
        private java.math.BigDecimal discountAmount;
        private String priceType;

        // Getters and Setters
        public Long getProductId() {
            return productId;
        }

        public void setProductId(Long productId) {
            this.productId = productId;
        }

        public java.math.BigDecimal getUnitPrice() {
            return unitPrice;
        }

        public void setUnitPrice(java.math.BigDecimal unitPrice) {
            this.unitPrice = unitPrice;
        }

        public java.math.BigDecimal getTotalPrice() {
            return totalPrice;
        }

        public void setTotalPrice(java.math.BigDecimal totalPrice) {
            this.totalPrice = totalPrice;
        }

        public java.math.BigDecimal getDiscountAmount() {
            return discountAmount;
        }

        public void setDiscountAmount(java.math.BigDecimal discountAmount) {
            this.discountAmount = discountAmount;
        }

        public String getPriceType() {
            return priceType;
        }

        public void setPriceType(String priceType) {
            this.priceType = priceType;
        }
    }
}
