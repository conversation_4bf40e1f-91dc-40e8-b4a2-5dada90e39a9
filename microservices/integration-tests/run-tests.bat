@echo off
chcp 65001 > nul
setlocal enabledelayedexpansion

echo ========================================
echo 集成测试执行脚本
echo ========================================
echo.

set SCRIPT_DIR=%~dp0
set PROJECT_ROOT=%SCRIPT_DIR%..\..\

echo 当前目录: %SCRIPT_DIR%
echo 项目根目录: %PROJECT_ROOT%
echo.

echo ========================================
echo 第一步：检查服务状态
echo ========================================

echo 检查 product-service 健康状态...
curl -s http://localhost:8082/health --connect-timeout 5 || echo product-service 未就绪

echo 检查 order-service 健康状态...
curl -s http://localhost:8084/health --connect-timeout 5 || echo order-service 未就绪

echo.
echo ========================================
echo 第二步：编译集成测试
echo ========================================

echo 编译集成测试模块...
cd /d %SCRIPT_DIR%
mvn clean compile test-compile

if %ERRORLEVEL% neq 0 (
    echo 集成测试编译失败！
    pause
    exit /b 1
)

echo.
echo ========================================
echo 第三步：执行集成测试
echo ========================================

echo.
echo 选择要执行的测试类型：
echo 1. 简化集成测试 (SimpleIntegrationTest)
echo 2. JSON序列化测试 (JsonSerializationTest)
echo 3. Saga分布式事务测试 (SagaTransactionTest)
echo 4. 性能监控测试 (PerformanceMonitoringTest)
echo 5. 执行所有测试
echo 6. 仅执行健康检查测试
echo.

set /p choice=请选择测试类型 (1-6): 

if "%choice%"=="1" (
    echo 执行简化集成测试...
    mvn test -Dtest=SimpleIntegrationTest
) else if "%choice%"=="2" (
    echo 执行JSON序列化测试...
    mvn test -Dtest=JsonSerializationTest
) else if "%choice%"=="3" (
    echo 执行Saga分布式事务测试...
    mvn test -Dtest=SagaTransactionTest
) else if "%choice%"=="4" (
    echo 执行性能监控测试...
    mvn test -Dtest=PerformanceMonitoringTest
) else if "%choice%"=="5" (
    echo 执行所有集成测试...
    mvn test
) else if "%choice%"=="6" (
    echo 执行健康检查测试...
    mvn test -Dtest=SimpleIntegrationTest#testServiceHealth
) else (
    echo 无效选择，执行默认健康检查测试...
    mvn test -Dtest=SimpleIntegrationTest#testServiceHealth
)

if %ERRORLEVEL% neq 0 (
    echo 集成测试执行失败！
    pause
    exit /b 1
)

echo.
echo ========================================
echo 第四步：生成测试报告
echo ========================================

echo 生成测试报告...
mvn surefire-report:report

echo.
echo ========================================
echo 集成测试完成！
echo ========================================
echo 测试报告位置: %SCRIPT_DIR%target\site\surefire-report.html
echo.

pause
