# 跨服务集成测试配置
quarkus:
  application:
    name: integration-tests
    version: 2.0.0

  # HTTP配置
  http:
    port: 8090
    test-port: 8091

  # 日志配置
  log:
    level: INFO
    category:
      "com.visthink": DEBUG
      "io.quarkus.test": DEBUG
    console:
      enable: true
      format: "%d{HH:mm:ss} %-5p [%c{2.}] (%t) %s%e%n"

  # REST客户端配置 - 指向各个微服务
  rest-client:
    # 订单服务客户端
    order-service:
      url: ${ORDER_SERVICE_URL:http://localhost:8084}
      scope: jakarta.inject.Singleton
      connect-timeout: 5000
      read-timeout: 30000

    # 商品服务客户端
    product-service:
      url: ${PRODUCT_SERVICE_URL:http://localhost:8082}
      scope: jakarta.inject.Singleton
      connect-timeout: 5000
      read-timeout: 30000

    # 库存服务客户端
    inventory-service:
      url: ${INVENTORY_SERVICE_URL:http://localhost:8083}
      scope: jakarta.inject.Singleton
      connect-timeout: 5000
      read-timeout: 30000

    # 用户服务客户端
    member-service:
      url: ${MEMBER_CENTER_URL:http://localhost:8081}
      scope: jakarta.inject.Singleton
      connect-timeout: 5000
      read-timeout: 30000

# 测试配置
test:
  # 测试租户配置
  tenant:
    default-id: 1
    test-id: 999

  # 测试数据配置
  data:
    # 测试商品ID
    product-ids:
      - 1
      - 2
      - 3
    # 测试用户ID
    user-ids:
      - 1
      - 2

  # 性能测试配置
  performance:
    # API响应时间要求（毫秒）
    max-response-time: 500
    # 并发测试线程数
    concurrent-threads: 10
    # 测试持续时间（秒）
    test-duration: 30

  # 重试配置
  retry:
    max-attempts: 3
    delay-seconds: 1

# 环境特定配置
"%test":
  quarkus:
    log:
      level: DEBUG
      category:
        "com.visthink": DEBUG
        "org.testcontainers": INFO
        "io.rest-assured": DEBUG

# 集成测试环境配置
"%integration":
  quarkus:
    rest-client:
      order-service:
        url: http://order-service:8084
      product-service:
        url: http://product-service:8082
      inventory-service:
        url: http://inventory-service:8083
      member-service:
        url: http://member-center:8081
