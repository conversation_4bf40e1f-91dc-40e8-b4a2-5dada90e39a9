package com.visthink.integration;

import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.junit.jupiter.api.*;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static io.restassured.RestAssured.given;
import static org.hamcrest.Matchers.*;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 简化的跨服务集成测试
 * 不依赖Quarkus测试框架，直接使用REST Assured进行HTTP调用测试
 * 
 * <AUTHOR>
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@DisplayName("简化跨服务集成测试")
public class SimpleIntegrationTest {

    // 服务配置
    private static final String ORDER_SERVICE_URL = "http://localhost:8084";
    private static final String PRODUCT_SERVICE_URL = "http://localhost:8082";
    private static final String TENANT_HEADER = "X-Tenant-Id";
    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final int MAX_RESPONSE_TIME = 500;

    @BeforeAll
    static void setupClass() {
        System.out.println("=== 开始简化跨服务集成测试 ===");
        RestAssured.enableLoggingOfRequestAndResponseIfValidationFails();
    }

    @AfterAll
    static void tearDownClass() {
        System.out.println("=== 简化跨服务集成测试完成 ===");
    }

    /**
     * 测试1：验证服务健康状态
     */
    @Test
    @Order(1)
    @DisplayName("1. 验证微服务健康状态")
    void testServicesHealth() {
        System.out.println("测试1：验证微服务健康状态");

        // 测试product-service健康状态
        try {
            Response productHealthResponse = given()
                    .when()
                    .get(PRODUCT_SERVICE_URL + "/health")
                    .then()
                    .statusCode(200)
                    .extract().response();

            System.out.println("✅ product-service健康状态正常");
            System.out.println("响应内容: " + productHealthResponse.asString());
        } catch (Exception e) {
            fail("❌ product-service健康检查失败: " + e.getMessage());
        }

        // 测试order-service健康状态
        try {
            Response orderHealthResponse = given()
                    .when()
                    .get(ORDER_SERVICE_URL + "/health")
                    .then()
                    .statusCode(200)
                    .extract().response();

            System.out.println("✅ order-service健康状态正常");
            System.out.println("响应内容: " + orderHealthResponse.asString());
        } catch (Exception e) {
            fail("❌ order-service健康检查失败: " + e.getMessage());
        }
    }

    /**
     * 测试2：商品验证接口测试
     */
    @Test
    @Order(2)
    @DisplayName("2. 商品验证接口测试")
    void testProductValidation() {
        System.out.println("测试2：商品验证接口测试");

        Long testProductId = 1L;
        long startTime = System.currentTimeMillis();

        try {
            Response response = given()
                    .header(TENANT_HEADER, DEFAULT_TENANT_ID)
                    .when()
                    .get(PRODUCT_SERVICE_URL + "/api/products/" + testProductId + "/validate")
                    .then()
                    .statusCode(200)
                    .extract().response();

            long responseTime = System.currentTimeMillis() - startTime;

            System.out.println("✅ 商品验证接口调用成功");
            System.out.println("响应时间: " + responseTime + "ms");
            System.out.println("响应内容: " + response.asString());

            // 验证响应时间
            assertTrue(responseTime < MAX_RESPONSE_TIME,
                    "API响应时间应小于" + MAX_RESPONSE_TIME + "ms，实际: " + responseTime + "ms");

        } catch (Exception e) {
            fail("❌ 商品验证接口测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试3：商品价格查询接口测试
     */
    @Test
    @Order(3)
    @DisplayName("3. 商品价格查询接口测试")
    void testProductPriceQuery() {
        System.out.println("测试3：商品价格查询接口测试");

        Long testProductId = 1L;
        Integer quantity = 2;
        long startTime = System.currentTimeMillis();

        try {
            Response response = given()
                    .header(TENANT_HEADER, DEFAULT_TENANT_ID)
                    .queryParam("quantity", quantity)
                    .when()
                    .get(PRODUCT_SERVICE_URL + "/api/products/" + testProductId + "/price")
                    .then()
                    .statusCode(200)
                    .extract().response();

            long responseTime = System.currentTimeMillis() - startTime;

            System.out.println("✅ 商品价格查询接口调用成功");
            System.out.println("响应时间: " + responseTime + "ms");
            System.out.println("响应内容: " + response.asString());

            // 验证响应时间
            assertTrue(responseTime < MAX_RESPONSE_TIME,
                    "API响应时间应小于" + MAX_RESPONSE_TIME + "ms，实际: " + responseTime + "ms");

        } catch (Exception e) {
            fail("❌ 商品价格查询接口测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试4：批量商品查询接口测试
     */
    @Test
    @Order(4)
    @DisplayName("4. 批量商品查询接口测试")
    void testBatchProductQuery() {
        System.out.println("测试4：批量商品查询接口测试");

        List<Long> productIds = List.of(1L, 2L, 3L);
        long startTime = System.currentTimeMillis();

        try {
            Response response = given()
                    .header(TENANT_HEADER, DEFAULT_TENANT_ID)
                    .contentType(ContentType.JSON)
                    .body(productIds)
                    .when()
                    .post(PRODUCT_SERVICE_URL + "/api/products/batch")
                    .then()
                    .statusCode(200)
                    .extract().response();

            long responseTime = System.currentTimeMillis() - startTime;

            System.out.println("✅ 批量商品查询接口调用成功");
            System.out.println("响应时间: " + responseTime + "ms");
            System.out.println("响应内容: " + response.asString());

            // 验证响应时间
            assertTrue(responseTime < MAX_RESPONSE_TIME,
                    "API响应时间应小于" + MAX_RESPONSE_TIME + "ms，实际: " + responseTime + "ms");

        } catch (Exception e) {
            fail("❌ 批量商品查询接口测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试5：订单服务API测试
     */
    @Test
    @Order(5)
    @DisplayName("5. 订单服务API测试")
    void testOrderServiceAPI() {
        System.out.println("测试5：订单服务API测试");

        long startTime = System.currentTimeMillis();

        try {
            Response response = given()
                    .header(TENANT_HEADER, DEFAULT_TENANT_ID)
                    .when()
                    .get(ORDER_SERVICE_URL + "/api/orders")
                    .then()
                    .statusCode(200)
                    .extract().response();

            long responseTime = System.currentTimeMillis() - startTime;

            System.out.println("✅ 订单服务API调用成功");
            System.out.println("响应时间: " + responseTime + "ms");
            System.out.println("响应内容: " + response.asString());

            // 验证响应时间
            assertTrue(responseTime < MAX_RESPONSE_TIME,
                    "API响应时间应小于" + MAX_RESPONSE_TIME + "ms，实际: " + responseTime + "ms");

        } catch (Exception e) {
            fail("❌ 订单服务API测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试6：跨服务集成场景测试
     */
    @Test
    @Order(6)
    @DisplayName("6. 跨服务集成场景测试")
    void testCrossServiceIntegration() {
        System.out.println("测试6：跨服务集成场景测试");

        String businessId = "INTEGRATION_TEST_" + UUID.randomUUID().toString().substring(0, 8);

        try {
            // 步骤1：验证商品存在
            System.out.println("步骤1：验证商品存在");
            Response productValidation = given()
                    .header(TENANT_HEADER, DEFAULT_TENANT_ID)
                    .when()
                    .get(PRODUCT_SERVICE_URL + "/api/products/1/validate")
                    .then()
                    .statusCode(200)
                    .extract().response();

            System.out.println("商品验证结果: " + productValidation.asString());

            // 步骤2：获取商品价格
            System.out.println("步骤2：获取商品价格");
            Response priceInfo = given()
                    .header(TENANT_HEADER, DEFAULT_TENANT_ID)
                    .queryParam("quantity", 1)
                    .when()
                    .get(PRODUCT_SERVICE_URL + "/api/products/1/price")
                    .then()
                    .statusCode(200)
                    .extract().response();

            System.out.println("商品价格信息: " + priceInfo.asString());

            // 步骤3：模拟订单创建（简化版）
            System.out.println("步骤3：模拟订单创建");
            Map<String, Object> orderRequest = Map.of(
                    "businessId", businessId,
                    "customerId", 1L,
                    "orderItems", List.of(
                            Map.of(
                                    "productId", 1L,
                                    "quantity", 1,
                                    "unitPrice", 99.99
                            )
                    ),
                    "totalAmount", 99.99,
                    "remark", "跨服务集成测试"
            );

            Response orderCreation = given()
                    .header(TENANT_HEADER, DEFAULT_TENANT_ID)
                    .contentType(ContentType.JSON)
                    .body(orderRequest)
                    .when()
                    .post(ORDER_SERVICE_URL + "/api/orders")
                    .then()
                    .statusCode(anyOf(is(200), is(201), is(400), is(422))) // 允许多种状态码
                    .extract().response();

            System.out.println("订单创建结果: " + orderCreation.asString());
            System.out.println("✅ 跨服务集成场景测试完成");

        } catch (Exception e) {
            fail("❌ 跨服务集成场景测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试7：并发性能测试
     */
    @Test
    @Order(7)
    @DisplayName("7. 并发性能测试")
    void testConcurrentPerformance() {
        System.out.println("测试7：并发性能测试");

        int concurrentRequests = 5; // 减少并发数量避免过载
        Long testProductId = 1L;

        try {
            long startTime = System.currentTimeMillis();

            // 并发测试商品验证接口
            List<Response> responses = java.util.stream.IntStream.range(0, concurrentRequests)
                    .parallel()
                    .mapToObj(i -> given()
                            .header(TENANT_HEADER, DEFAULT_TENANT_ID)
                            .when()
                            .get(PRODUCT_SERVICE_URL + "/api/products/" + testProductId + "/validate"))
                    .toList();

            long totalTime = System.currentTimeMillis() - startTime;

            // 验证所有请求都成功
            responses.forEach(response -> {
                assertEquals(200, response.getStatusCode(), "并发请求应该成功");
            });

            double avgResponseTime = (double) totalTime / concurrentRequests;
            System.out.println("✅ 并发性能测试完成");
            System.out.println("并发请求数: " + concurrentRequests);
            System.out.println("总耗时: " + totalTime + "ms");
            System.out.println("平均响应时间: " + avgResponseTime + "ms");

            // 验证平均响应时间
            assertTrue(avgResponseTime < MAX_RESPONSE_TIME,
                    "平均响应时间应小于" + MAX_RESPONSE_TIME + "ms，实际: " + avgResponseTime + "ms");

        } catch (Exception e) {
            fail("❌ 并发性能测试失败: " + e.getMessage());
        }
    }
}
