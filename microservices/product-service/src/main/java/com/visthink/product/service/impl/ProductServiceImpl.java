package com.visthink.product.service.impl;

import com.visthink.common.dto.PageResult;
import com.visthink.product.entity.Product;
import com.visthink.product.entity.ProductSku;
import com.visthink.product.repository.ProductRepository;
import com.visthink.product.repository.ProductSkuRepository;
import com.visthink.product.service.ProductService;
import com.visthink.product.dto.request.ProductCreateRequest;
import com.visthink.product.dto.request.ProductUpdateRequest;
import com.visthink.product.dto.query.ProductQueryRequest;
import com.visthink.product.dto.response.ProductResponse;
import com.visthink.product.dto.response.ProductSkuResponse;
import com.visthink.product.exception.BusinessException;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.quarkus.panache.common.Page;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商品管理服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@ApplicationScoped
public class ProductServiceImpl implements ProductService {

    @Inject
    ProductRepository productRepository;

    @Inject
    ProductSkuRepository productSkuRepository;

    @Override
    @WithTransaction
    public Uni<ProductResponse> createProduct(Long tenantId, ProductCreateRequest request) {
        log.info("创建商品: tenantId={}, productCode={}", tenantId, request.getProductCode());

        // 检查商品编码是否已存在
        return productRepository.existsByTenantAndCode(tenantId, request.getProductCode())
                .onItem().transformToUni(exists -> {
                    if (exists) {
                        return Uni.createFrom().failure(
                            new BusinessException("PRODUCT_CODE_EXISTS", "商品编码已存在"));
                    }

                    // 创建商品
                    Product product = convertToEntity(tenantId, request);

                    return productRepository.persist(product)
                            .onItem().transformToUni(savedProduct -> {
                                // 创建SKU
                                if (request.getSkuList() != null && !request.getSkuList().isEmpty()) {
                                    return createProductSkus(tenantId, savedProduct.id, savedProduct.getProductCode(), request.getSkuList())
                                            .map(skus -> convertToResponse(savedProduct, skus));
                                }
                                return Uni.createFrom().item(convertToResponse(savedProduct, Collections.emptyList()));
                            });
                });
    }

    @Override
    @WithTransaction
    public Uni<ProductResponse> updateProduct(Long tenantId, Long productId, ProductUpdateRequest request) {
        log.info("更新商品: tenantId={}, productId={}", tenantId, productId);

        return productRepository.findById(productId)
                .onItem().transformToUni(product -> {
                    if (product == null || !product.getTenantId().equals(tenantId)) {
                        return Uni.createFrom().failure(
                            new BusinessException("PRODUCT_NOT_FOUND", "商品不存在"));
                    }

                    // 更新商品信息
                    updateProductFromRequest(product, request);

                    return productRepository.persist(product)
                            .onItem().transformToUni(savedProduct ->
                                productSkuRepository.findByProductId(productId)
                                    .map(skus -> convertToResponse(savedProduct, skus))
                            );
                });
    }

    @Override
    @WithSession
    public Uni<ProductResponse> getProductById(Long tenantId, Long productId) {
        return productRepository.findById(productId)
                .onItem().transformToUni(product -> {
                    if (product == null || !product.getTenantId().equals(tenantId)) {
                        return Uni.createFrom().failure(
                            new BusinessException("PRODUCT_NOT_FOUND", "商品不存在"));
                    }

                    return productSkuRepository.findByProductId(productId)
                            .map(skus -> convertToResponse(product, skus));
                });
    }

    @Override
    @WithSession
    public Uni<ProductResponse> getProductByCode(Long tenantId, String productCode) {
        return productRepository.findByTenantAndCode(tenantId, productCode)
                .onItem().transformToUni(product -> {
                    if (product == null) {
                        return Uni.createFrom().failure(
                            new BusinessException("PRODUCT_NOT_FOUND", "商品不存在"));
                    }

                    return productSkuRepository.findByProductId(product.id)
                            .map(skus -> convertToResponse(product, skus));
                });
    }

    @Override
    @WithSession
    public Uni<PageResult<ProductResponse>> getProductList(Long tenantId, ProductQueryRequest request) {
        Page page = Page.of(request.getPage() - 1, request.getSize());

        return productRepository.findByTenantId(tenantId, page)
                .onItem().transformToUni(products -> {
                    if (products.isEmpty()) {
                        return Uni.createFrom().item(new PageResult<>(Collections.emptyList(), 0L, request.getPage(), request.getSize()));
                    }

                    List<Long> productIds = products.stream().map(p -> p.id).collect(Collectors.toList());

                    return productSkuRepository.findByProductIds(productIds)
                            .onItem().transformToUni(skus -> {
                                Map<Long, List<ProductSku>> skuMap = skus.stream()
                                        .collect(Collectors.groupingBy(sku -> sku.getProductId()));

                                List<ProductResponse> responses = products.stream()
                                        .map(product -> convertToResponse(product, skuMap.getOrDefault(product.id, Collections.emptyList())))
                                        .collect(Collectors.toList());

                                return productRepository.countByTenant(tenantId)
                                        .map(total -> new PageResult<>(responses, total, request.getPage(), request.getSize()));
                            });
                });
    }

    @Override
    @WithSession
    public Uni<PageResult<ProductResponse>> searchProducts(Long tenantId, String keyword, int page, int size) {
        Page pageRequest = Page.of(page - 1, size);

        return productRepository.searchProducts(tenantId, keyword, pageRequest)
                .onItem().transformToUni(products -> {
                    if (products.isEmpty()) {
                        return Uni.createFrom().item(new PageResult<>(Collections.emptyList(), 0L, page, size));
                    }

                    List<Long> productIds = products.stream().map(p -> p.id).collect(Collectors.toList());

                    return productSkuRepository.findByProductIds(productIds)
                            .onItem().transformToUni(skus -> {
                                Map<Long, List<ProductSku>> skuMap = skus.stream()
                                        .collect(Collectors.groupingBy(sku -> sku.getProductId()));

                                List<ProductResponse> responses = products.stream()
                                        .map(product -> convertToResponse(product, skuMap.getOrDefault(product.id, Collections.emptyList())))
                                        .collect(Collectors.toList());

                                // 简化计数，实际应该用搜索条件计数
                                return Uni.createFrom().item(new PageResult<>(responses, (long) responses.size(), page, size));
                            });
                });
    }

    @Override
    @WithTransaction
    public Uni<Boolean> deleteProduct(Long tenantId, Long productId) {
        return productRepository.findById(productId)
                .onItem().transformToUni(product -> {
                    if (product == null || !product.getTenantId().equals(tenantId)) {
                        return Uni.createFrom().failure(
                            new BusinessException("PRODUCT_NOT_FOUND", "商品不存在"));
                    }

                    // 软删除
                    product.setStatus(Product.Status.DELETED);
                    product.setUpdateTime(LocalDateTime.now());

                    return productRepository.persist(product)
                            .onItem().transformToUni(savedProduct ->
                                productSkuRepository.updateStatusByProductId(tenantId, productId, ProductSku.Status.DELETED)
                                    .map(count -> true)
                            );
                });
    }

    @Override
    @WithTransaction
    public Uni<Boolean> batchDeleteProducts(Long tenantId, List<Long> productIds) {
        return productRepository.findByTenantAndIds(tenantId, productIds)
                .onItem().transformToUni(products -> {
                    if (products.isEmpty()) {
                        return Uni.createFrom().item(false);
                    }

                    return productRepository.updateStatusByIds(tenantId, productIds, Product.Status.DELETED)
                            .onItem().transformToUni(count -> {
                                // 同时更新SKU状态
                                return productSkuRepository.findByProductIds(productIds)
                                        .onItem().transformToUni(skus -> {
                                            List<Long> skuIds = skus.stream().map(sku -> sku.id).collect(Collectors.toList());
                                            if (skuIds.isEmpty()) {
                                                return Uni.createFrom().item(true);
                                            }
                                            return productSkuRepository.updateStatusByIds(tenantId, skuIds, ProductSku.Status.DELETED)
                                                    .map(skuCount -> true);
                                        });
                            });
                });
    }

    @Override
    @WithTransaction
    public Uni<Boolean> publishProduct(Long tenantId, Long productId) {
        return updateProductStatus(tenantId, productId, Product.Status.NORMAL);
    }

    @Override
    @WithTransaction
    public Uni<Boolean> unpublishProduct(Long tenantId, Long productId) {
        return updateProductStatus(tenantId, productId, Product.Status.DISABLED);
    }

    @Override
    @WithTransaction
    public Uni<Boolean> batchPublishProducts(Long tenantId, List<Long> productIds) {
        return batchUpdateProductStatus(tenantId, productIds, Product.Status.NORMAL);
    }

    @Override
    @WithTransaction
    public Uni<Boolean> batchUnpublishProducts(Long tenantId, List<Long> productIds) {
        return batchUpdateProductStatus(tenantId, productIds, Product.Status.DISABLED);
    }

    // 其他方法的简化实现，返回默认值或抛出未实现异常
    @Override
    public Uni<Boolean> publishToAllPlatforms(Long tenantId, Long productId) {
        // TODO: 实现平台发布逻辑
        return Uni.createFrom().item(true);
    }

    @Override
    public Uni<Boolean> publishToPlatform(Long tenantId, Long productId, String platformCode) {
        // TODO: 实现平台发布逻辑
        return Uni.createFrom().item(true);
    }

    @Override
    public Uni<Boolean> syncProductToPlatform(Long tenantId, Long productId, String platformCode) {
        // TODO: 实现平台同步逻辑
        return Uni.createFrom().item(true);
    }

    @Override
    public Uni<Boolean> batchSyncProductsToPlatform(Long tenantId, List<Long> productIds, String platformCode) {
        // TODO: 实现批量平台同步逻辑
        return Uni.createFrom().item(true);
    }

    @Override
    public Uni<Map<String, Object>> getProductStatistics(Long tenantId) {
        return productRepository.countByTenant(tenantId)
                .onItem().transformToUni(total ->
                    productRepository.countByTenantAndStatus(tenantId, Product.Status.NORMAL)
                        .onItem().transformToUni(normal ->
                            productRepository.countByTenantAndStatus(tenantId, Product.Status.DISABLED)
                                .map(disabled -> {
                                    Map<String, Object> stats = new HashMap<>();
                                    stats.put("total", total);
                                    stats.put("normal", normal);
                                    stats.put("disabled", disabled);
                                    stats.put("deleted", total - normal - disabled);
                                    return stats;
                                })
                        )
                );
    }

    // 其他方法的简化实现...
    @Override
    public Uni<PageResult<ProductResponse>> getHotProducts(Long tenantId, int page, int size) {
        Page pageRequest = Page.of(page - 1, size);
        return productRepository.findHotProducts(tenantId, pageRequest)
                .map(products -> new PageResult<>(
                    products.stream().map(p -> convertToResponse(p, Collections.emptyList())).collect(Collectors.toList()),
                    (long) products.size(), page, size));
    }

    @Override
    public Uni<PageResult<ProductResponse>> getRecommendProducts(Long tenantId, int page, int size) {
        Page pageRequest = Page.of(page - 1, size);
        return productRepository.findRecommendProducts(tenantId, pageRequest)
                .map(products -> new PageResult<>(
                    products.stream().map(p -> convertToResponse(p, Collections.emptyList())).collect(Collectors.toList()),
                    (long) products.size(), page, size));
    }

    @Override
    public Uni<PageResult<ProductResponse>> getNewProducts(Long tenantId, int page, int size) {
        Page pageRequest = Page.of(page - 1, size);
        return productRepository.findNewProducts(tenantId, pageRequest)
                .map(products -> new PageResult<>(
                    products.stream().map(p -> convertToResponse(p, Collections.emptyList())).collect(Collectors.toList()),
                    (long) products.size(), page, size));
    }

    @Override
    public Uni<PageResult<ProductResponse>> getProductsByPriceRange(Long tenantId, Double minPrice, Double maxPrice, int page, int size) {
        Page pageRequest = Page.of(page - 1, size);
        return productRepository.findByPriceRange(tenantId, minPrice, maxPrice, pageRequest)
                .map(products -> new PageResult<>(
                    products.stream().map(p -> convertToResponse(p, Collections.emptyList())).collect(Collectors.toList()),
                    (long) products.size(), page, size));
    }

    @Override
    public Uni<Boolean> incrementViewCount(Long productId) {
        return productRepository.incrementViewCount(productId).map(count -> count > 0);
    }

    @Override
    public Uni<Boolean> incrementSalesCount(Long productId, Integer quantity) {
        return productRepository.incrementSalesCount(productId, quantity).map(count -> count > 0);
    }

    @Override
    public Uni<Boolean> checkProductCodeExists(Long tenantId, String productCode) {
        return productRepository.existsByTenantAndCode(tenantId, productCode);
    }

    @Override
    public Uni<PageResult<ProductResponse>> getProductsByCategory(Long tenantId, Long categoryId, int page, int size) {
        Page pageRequest = Page.of(page - 1, size);
        return productRepository.findByTenantAndCategory(tenantId, categoryId, pageRequest)
                .map(products -> new PageResult<>(
                    products.stream().map(p -> convertToResponse(p, Collections.emptyList())).collect(Collectors.toList()),
                    (long) products.size(), page, size));
    }

    @Override
    public Uni<List<ProductResponse>> getLowStockProducts(Long tenantId) {
        // TODO: 实现库存不足商品查询
        return Uni.createFrom().item(Collections.emptyList());
    }

    @Override
    @WithTransaction
    public Uni<Boolean> batchUpdateProductStatus(Long tenantId, List<Long> productIds, Integer status) {
        return productRepository.updateStatusByIds(tenantId, productIds, status).map(count -> count > 0);
    }

    @Override
    public Uni<ProductResponse> copyProduct(Long tenantId, Long productId, String newProductCode) {
        // TODO: 实现商品复制逻辑
        return Uni.createFrom().failure(new BusinessException("NOT_IMPLEMENTED", "功能未实现"));
    }

    @Override
    public Uni<List<ProductResponse>> exportProducts(Long tenantId, ProductQueryRequest request) {
        // TODO: 实现商品导出逻辑
        return Uni.createFrom().item(Collections.emptyList());
    }

    @Override
    public Uni<Map<String, Object>> importProducts(Long tenantId, List<ProductCreateRequest> products) {
        // TODO: 实现商品导入逻辑
        Map<String, Object> result = new HashMap<>();
        result.put("success", 0);
        result.put("failed", 0);
        result.put("total", products.size());
        return Uni.createFrom().item(result);
    }

    // 私有辅助方法
    private Uni<Boolean> updateProductStatus(Long tenantId, Long productId, Integer status) {
        return productRepository.findById(productId)
                .onItem().transformToUni(product -> {
                    if (product == null || !product.getTenantId().equals(tenantId)) {
                        return Uni.createFrom().failure(
                            new BusinessException("PRODUCT_NOT_FOUND", "商品不存在"));
                    }

                    product.setStatus(status);
                    product.setUpdateTime(LocalDateTime.now());

                    return productRepository.persist(product).map(p -> true);
                });
    }

    private Uni<List<ProductSku>> createProductSkus(Long tenantId, Long productId, String productCode,
                                                   List<ProductCreateRequest.ProductSkuCreateRequest> skuRequests) {
        List<ProductSku> skus = skuRequests.stream()
                .map(request -> convertSkuToEntity(tenantId, productId, productCode, request))
                .collect(Collectors.toList());

        return productSkuRepository.persist(skus)
                .map(v -> skus); // 返回保存的SKU列表
    }

    private Product convertToEntity(Long tenantId, ProductCreateRequest request) {
        Product product = new Product();
        product.setTenantId(tenantId);
        product.setProductCode(request.getProductCode());
        product.setProductName(request.getProductName());
        product.setShortName(request.getShortName());
        product.setCategoryId(request.getCategoryId());
        product.setBrandId(request.getBrandId());
        product.setProductType(request.getProductType());
        product.setStatus(request.getStatus());
        product.setDescription(request.getDescription());
        product.setDetailContent(request.getDetailContent());
        product.setMainImageUrl(request.getMainImageUrl());
        product.setImageUrls(request.getImageUrls());
        product.setVideoUrl(request.getVideoUrl());
        product.setMarketPrice(request.getMarketPrice());
        product.setSalePrice(request.getSalePrice());
        product.setCostPrice(request.getCostPrice());
        product.setWeight(request.getWeight());
        product.setLength(request.getLength());
        product.setWidth(request.getWidth());
        product.setHeight(request.getHeight());
        product.setMultiSpec(request.getMultiSpec());
        product.setSpecParams(request.getSpecParams());
        product.setAttributes(request.getAttributes());
        product.setKeywords(request.getKeywords());
        product.setSortOrder(request.getSortOrder());
        product.setIsRecommend(request.getIsRecommend());
        product.setIsHot(request.getIsHot());
        product.setIsNew(request.getIsNew());
        product.setCreateTime(LocalDateTime.now());
        product.setUpdateTime(LocalDateTime.now());
        return product;
    }

    private void updateProductFromRequest(Product product, ProductUpdateRequest request) {
        if (request.getProductName() != null) {
            product.setProductName(request.getProductName());
        }
        if (request.getShortName() != null) {
            product.setShortName(request.getShortName());
        }
        if (request.getCategoryId() != null) {
            product.setCategoryId(request.getCategoryId());
        }
        if (request.getBrandId() != null) {
            product.setBrandId(request.getBrandId());
        }
        if (request.getProductType() != null) {
            product.setProductType(request.getProductType());
        }
        if (request.getStatus() != null) {
            product.setStatus(request.getStatus());
        }
        if (request.getDescription() != null) {
            product.setDescription(request.getDescription());
        }
        if (request.getDetailContent() != null) {
            product.setDetailContent(request.getDetailContent());
        }
        if (request.getMainImageUrl() != null) {
            product.setMainImageUrl(request.getMainImageUrl());
        }
        if (request.getImageUrls() != null) {
            product.setImageUrls(request.getImageUrls());
        }
        if (request.getVideoUrl() != null) {
            product.setVideoUrl(request.getVideoUrl());
        }
        if (request.getMarketPrice() != null) {
            product.setMarketPrice(request.getMarketPrice());
        }
        if (request.getSalePrice() != null) {
            product.setSalePrice(request.getSalePrice());
        }
        if (request.getCostPrice() != null) {
            product.setCostPrice(request.getCostPrice());
        }
        if (request.getWeight() != null) {
            product.setWeight(request.getWeight());
        }
        if (request.getLength() != null) {
            product.setLength(request.getLength());
        }
        if (request.getWidth() != null) {
            product.setWidth(request.getWidth());
        }
        if (request.getHeight() != null) {
            product.setHeight(request.getHeight());
        }
        if (request.getMultiSpec() != null) {
            product.setMultiSpec(request.getMultiSpec());
        }
        if (request.getSpecParams() != null) {
            product.setSpecParams(request.getSpecParams());
        }
        if (request.getAttributes() != null) {
            product.setAttributes(request.getAttributes());
        }
        if (request.getKeywords() != null) {
            product.setKeywords(request.getKeywords());
        }
        if (request.getSortOrder() != null) {
            product.setSortOrder(request.getSortOrder());
        }
        if (request.getIsRecommend() != null) {
            product.setIsRecommend(request.getIsRecommend());
        }
        if (request.getIsHot() != null) {
            product.setIsHot(request.getIsHot());
        }
        if (request.getIsNew() != null) {
            product.setIsNew(request.getIsNew());
        }
        product.setUpdateTime(LocalDateTime.now());
    }

    private ProductSku convertSkuToEntity(Long tenantId, Long productId, String productCode,
                                         ProductCreateRequest.ProductSkuCreateRequest request) {
        ProductSku sku = new ProductSku();
        sku.setTenantId(tenantId);
        sku.setProductId(productId);
        sku.setProductCode(productCode);
        sku.setSkuCode(request.getSkuCode());
        sku.setSkuName(request.getSkuName());
        sku.setSpecValues(request.getSpecValues());
        sku.setImageUrl(request.getImageUrl());
        sku.setMarketPrice(request.getMarketPrice());
        sku.setSalePrice(request.getSalePrice());
        sku.setCostPrice(request.getCostPrice());
        sku.setWeight(request.getWeight());
        sku.setStockQuantity(request.getStockQuantity());
        sku.setWarningStock(request.getWarningStock());
        sku.setStatus(request.getStatus());
        sku.setBarcode(request.getBarcode());
        sku.setSortOrder(request.getSortOrder());
        sku.setCreateTime(LocalDateTime.now());
        sku.setUpdateTime(LocalDateTime.now());
        return sku;
    }

    private ProductResponse convertToResponse(Product product, List<ProductSku> skus) {
        ProductResponse response = new ProductResponse();
        response.setId(product.id);
        response.setTenantId(product.getTenantId());
        response.setProductCode(product.getProductCode());
        response.setProductName(product.getProductName());
        response.setShortName(product.getShortName());
        response.setCategoryId(product.getCategoryId());
        response.setBrandId(product.getBrandId());
        response.setProductType(product.getProductType());
        response.setStatus(product.getStatus());
        response.setDescription(product.getDescription());
        response.setDetailContent(product.getDetailContent());
        response.setMainImageUrl(product.getMainImageUrl());
        response.setImageUrls(product.getImageUrls());
        response.setVideoUrl(product.getVideoUrl());
        response.setMarketPrice(product.getMarketPrice());
        response.setSalePrice(product.getSalePrice());
        response.setCostPrice(product.getCostPrice());
        response.setWeight(product.getWeight());
        response.setLength(product.getLength());
        response.setWidth(product.getWidth());
        response.setHeight(product.getHeight());
        response.setMultiSpec(product.getMultiSpec());
        response.setSpecParams(product.getSpecParams());
        response.setAttributes(product.getAttributes());
        response.setKeywords(product.getKeywords());
        response.setSortOrder(product.getSortOrder());
        response.setIsRecommend(product.getIsRecommend());
        response.setIsHot(product.getIsHot());
        response.setIsNew(product.getIsNew());
        response.setSalesCount(product.getSalesCount());
        response.setViewCount(product.getViewCount());
        response.setCreatedAt(product.getCreateTime());
        response.setUpdatedAt(product.getUpdateTime());
        response.setCreatedBy(product.getCreateBy());
        response.setUpdatedBy(product.getUpdateBy());

        // 转换SKU列表
        List<ProductSkuResponse> skuResponses = skus.stream()
                .map(this::convertSkuToResponse)
                .collect(Collectors.toList());
        response.setSkuList(skuResponses);

        // 计算统计信息
        response.setTotalStock(skus.stream().mapToInt(sku -> sku.getStockQuantity() != null ? sku.getStockQuantity() : 0).sum());

        return response;
    }

    private ProductSkuResponse convertSkuToResponse(ProductSku sku) {
        ProductSkuResponse response = new ProductSkuResponse();
        response.setId(sku.id);
        response.setTenantId(sku.getTenantId());
        response.setProductId(sku.getProductId());
        response.setProductCode(sku.getProductCode());
        response.setSkuCode(sku.getSkuCode());
        response.setSkuName(sku.getSkuName());
        response.setSpecValues(sku.getSpecValues());
        response.setImageUrl(sku.getImageUrl());
        response.setMarketPrice(sku.getMarketPrice());
        response.setSalePrice(sku.getSalePrice());
        response.setCostPrice(sku.getCostPrice());
        response.setWeight(sku.getWeight());
        response.setStockQuantity(sku.getStockQuantity());
        response.setWarningStock(sku.getWarningStock());
        response.setSalesCount(sku.getSalesCount());
        response.setStatus(sku.getStatus());
        response.setBarcode(sku.getBarcode());
        response.setSortOrder(sku.getSortOrder());
        response.setCreatedAt(sku.getCreateTime());
        response.setUpdatedAt(sku.getUpdateTime());
        response.setCreatedBy(sku.getCreateBy());
        response.setUpdatedBy(sku.getUpdateBy());
        return response;
    }
}
