package com.visthink.integration;

import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.junit.jupiter.api.*;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.IntStream;

import static io.restassured.RestAssured.given;
import static org.hamcrest.Matchers.*;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 性能监控集成测试
 *
 * 测试场景：
 * 1. API响应时间监控测试
 * 2. 并发负载测试
 * 3. 内存使用监控测试
 * 4. 数据库连接池监控测试
 * 5. 微服务健康状态监控测试
 * 6. 缓存性能测试
 * 7. 网络延迟测试
 *
 * <AUTHOR>
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@DisplayName("性能监控集成测试")
public class PerformanceMonitoringTest {

    private static final String ORDER_SERVICE_URL = "http://localhost:8084";
    private static final String PRODUCT_SERVICE_URL = "http://localhost:8082";
    private static final String TENANT_HEADER = "X-Tenant-Id";
    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final int MAX_RESPONSE_TIME = 500; // 500ms响应时间目标
    private static final int CONCURRENT_USERS = 10;
    private static final int TEST_DURATION_SECONDS = 30;

    @BeforeAll
    static void setupClass() {
        System.out.println("=== 开始性能监控集成测试 ===");
        RestAssured.enableLoggingOfRequestAndResponseIfValidationFails();
    }

    @AfterAll
    static void tearDownClass() {
        System.out.println("=== 性能监控集成测试完成 ===");
    }

    /**
     * 测试1：API响应时间监控测试
     */
    @Test
    @Order(1)
    @DisplayName("1. API响应时间监控测试")
    void testApiResponseTimeMonitoring() {
        System.out.println("测试1：API响应时间监控测试");

        int testRounds = 20;
        long totalResponseTime = 0;
        int successCount = 0;
        long maxResponseTime = 0;
        long minResponseTime = Long.MAX_VALUE;

        for (int i = 0; i < testRounds; i++) {
            try {
                long startTime = System.currentTimeMillis();

                Response response = given()
                        .header(TENANT_HEADER, DEFAULT_TENANT_ID)
                        .queryParam("page", 0)
                        .queryParam("size", 10)
                        .when()
                        .get(PRODUCT_SERVICE_URL + "/api/products")
                        .then()
                        .statusCode(anyOf(is(200), is(404), is(500))) // 允许多种状态码
                        .extract().response();

                long responseTime = System.currentTimeMillis() - startTime;

                if (response.getStatusCode() == 200) {
                    successCount++;
                    totalResponseTime += responseTime;
                    maxResponseTime = Math.max(maxResponseTime, responseTime);
                    minResponseTime = Math.min(minResponseTime, responseTime);

                    System.out.println("第" + (i + 1) + "次请求响应时间: " + responseTime + "ms");
                }

                // 短暂休息避免过载
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }

            } catch (Exception e) {
                System.out.println("第" + (i + 1) + "次请求失败: " + e.getMessage());
            }
        }

        if (successCount > 0) {
            double avgResponseTime = (double) totalResponseTime / successCount;
            double successRate = (double) successCount / testRounds * 100;

            System.out.println("=== API响应时间监控结果 ===");
            System.out.println("总请求次数: " + testRounds);
            System.out.println("成功次数: " + successCount);
            System.out.println("成功率: " + String.format("%.2f", successRate) + "%");
            System.out.println("平均响应时间: " + String.format("%.2f", avgResponseTime) + "ms");
            System.out.println("最大响应时间: " + maxResponseTime + "ms");
            System.out.println("最小响应时间: " + minResponseTime + "ms");

            // 验证性能目标
            assertTrue(avgResponseTime < MAX_RESPONSE_TIME,
                    "平均响应时间应小于" + MAX_RESPONSE_TIME + "ms，实际: " + avgResponseTime + "ms");
            assertTrue(successRate >= 90, "成功率应大于等于90%，实际: " + successRate + "%");

            System.out.println("✅ API响应时间监控测试通过");
        } else {
            System.out.println("⚠️ 所有请求都失败，可能服务未启动");
        }
    }

    /**
     * 测试2：并发负载测试
     */
    @Test
    @Order(2)
    @DisplayName("2. 并发负载测试")
    void testConcurrentLoadTesting() {
        System.out.println("测试2：并发负载测试");

        ExecutorService executor = Executors.newFixedThreadPool(CONCURRENT_USERS);
        long testStartTime = System.currentTimeMillis();

        try {
            // 创建并发任务
            List<CompletableFuture<Map<String, Object>>> futures = IntStream.range(0, CONCURRENT_USERS)
                    .mapToObj(userId -> CompletableFuture.supplyAsync(() -> {
                        int requestCount = 0;
                        int successCount = 0;
                        long totalResponseTime = 0;
                        long maxResponseTime = 0;

                        long userStartTime = System.currentTimeMillis();

                        while (System.currentTimeMillis() - userStartTime < TEST_DURATION_SECONDS * 1000) {
                            try {
                                long requestStartTime = System.currentTimeMillis();

                                Response response = given()
                                        .header(TENANT_HEADER, DEFAULT_TENANT_ID)
                                        .queryParam("page", requestCount % 5)
                                        .queryParam("size", 5)
                                        .when()
                                        .get(PRODUCT_SERVICE_URL + "/api/products")
                                        .then()
                                        .statusCode(anyOf(is(200), is(404), is(500)))
                                        .extract().response();

                                long responseTime = System.currentTimeMillis() - requestStartTime;
                                requestCount++;

                                if (response.getStatusCode() == 200) {
                                    successCount++;
                                    totalResponseTime += responseTime;
                                    maxResponseTime = Math.max(maxResponseTime, responseTime);
                                }

                                // 短暂休息模拟真实用户行为
                                try {
                                    Thread.sleep(50);
                                } catch (InterruptedException e) {
                                    Thread.currentThread().interrupt();
                                    break; // 中断时退出循环
                                }

                            } catch (Exception e) {
                                requestCount++;
                                // 继续执行，不中断测试
                            }
                        }

                        return Map.<String, Object>of(
                                "userId", userId,
                                "requestCount", requestCount,
                                "successCount", successCount,
                                "totalResponseTime", totalResponseTime,
                                "maxResponseTime", maxResponseTime
                        );
                    }, executor))
                    .toList();

            // 等待所有任务完成
            List<Map<String, Object>> results = futures.stream()
                    .map(CompletableFuture::join)
                    .toList();

            long testEndTime = System.currentTimeMillis();
            long totalTestTime = testEndTime - testStartTime;

            // 统计总体结果
            int totalRequests = results.stream().mapToInt(r -> (Integer) r.get("requestCount")).sum();
            int totalSuccesses = results.stream().mapToInt(r -> (Integer) r.get("successCount")).sum();
            long totalResponseTime = results.stream().mapToLong(r -> (Long) r.get("totalResponseTime")).sum();
            long maxResponseTime = results.stream().mapToLong(r -> (Long) r.get("maxResponseTime")).max().orElse(0);

            double avgResponseTime = totalSuccesses > 0 ? (double) totalResponseTime / totalSuccesses : 0;
            double successRate = totalRequests > 0 ? (double) totalSuccesses / totalRequests * 100 : 0;
            double throughput = (double) totalSuccesses / (totalTestTime / 1000.0);

            System.out.println("=== 并发负载测试结果 ===");
            System.out.println("测试时长: " + totalTestTime + "ms");
            System.out.println("并发用户数: " + CONCURRENT_USERS);
            System.out.println("总请求数: " + totalRequests);
            System.out.println("成功请求数: " + totalSuccesses);
            System.out.println("成功率: " + String.format("%.2f", successRate) + "%");
            System.out.println("平均响应时间: " + String.format("%.2f", avgResponseTime) + "ms");
            System.out.println("最大响应时间: " + maxResponseTime + "ms");
            System.out.println("吞吐量: " + String.format("%.2f", throughput) + " 请求/秒");

            // 验证性能目标
            if (totalSuccesses > 0) {
                assertTrue(avgResponseTime < MAX_RESPONSE_TIME,
                        "并发测试平均响应时间应小于" + MAX_RESPONSE_TIME + "ms，实际: " + avgResponseTime + "ms");
                assertTrue(successRate >= 80, "并发测试成功率应大于等于80%，实际: " + successRate + "%");
                assertTrue(throughput >= 1, "吞吐量应大于等于1请求/秒，实际: " + throughput);

                System.out.println("✅ 并发负载测试通过");
            } else {
                System.out.println("⚠️ 并发负载测试中没有成功的请求");
            }

        } finally {
            executor.shutdown();
            try {
                if (!executor.awaitTermination(10, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
            }
        }
    }

    /**
     * 测试3：微服务健康状态监控测试
     */
    @Test
    @Order(3)
    @DisplayName("3. 微服务健康状态监控测试")
    void testMicroserviceHealthMonitoring() {
        System.out.println("测试3：微服务健康状态监控测试");

        Map<String, String> services = Map.of(
                "product-service", PRODUCT_SERVICE_URL,
                "order-service", ORDER_SERVICE_URL
        );

        for (Map.Entry<String, String> service : services.entrySet()) {
            String serviceName = service.getKey();
            String serviceUrl = service.getValue();

            try {
                long startTime = System.currentTimeMillis();

                // 检查健康状态
                Response healthResponse = given()
                        .when()
                        .get(serviceUrl + "/health")
                        .then()
                        .statusCode(anyOf(is(200), is(503), is(404))) // 允许多种状态码
                        .extract().response();

                long responseTime = System.currentTimeMillis() - startTime;

                System.out.println(serviceName + " 健康检查:");
                System.out.println("  状态码: " + healthResponse.getStatusCode());
                System.out.println("  响应时间: " + responseTime + "ms");
                System.out.println("  响应内容: " + healthResponse.asString());

                // 检查指标端点
                try {
                    Response metricsResponse = given()
                            .when()
                            .get(serviceUrl + "/metrics")
                            .then()
                            .statusCode(anyOf(is(200), is(404))) // 允许指标端点不存在
                            .extract().response();

                    if (metricsResponse.getStatusCode() == 200) {
                        System.out.println("  指标端点可用");
                        String metricsContent = metricsResponse.asString();
                        if (metricsContent.contains("http_server_requests")) {
                            System.out.println("  包含HTTP请求指标");
                        }
                        if (metricsContent.contains("jvm_memory")) {
                            System.out.println("  包含JVM内存指标");
                        }
                    } else {
                        System.out.println("  指标端点不可用");
                    }
                } catch (Exception e) {
                    System.out.println("  指标端点检查失败: " + e.getMessage());
                }

                // 验证健康检查响应时间
                assertTrue(responseTime < 5000,
                          serviceName + " 健康检查响应时间应小于5秒，实际: " + responseTime + "ms");

            } catch (Exception e) {
                System.out.println(serviceName + " 健康检查失败: " + e.getMessage());
            }

            System.out.println();
        }

        System.out.println("✅ 微服务健康状态监控测试完成");
    }

    /**
     * 测试4：数据库连接性能测试
     */
    @Test
    @Order(4)
    @DisplayName("4. 数据库连接性能测试")
    void testDatabaseConnectionPerformance() {
        System.out.println("测试4：数据库连接性能测试");

        int testRounds = 10;
        long totalDbResponseTime = 0;
        int successCount = 0;

        for (int i = 0; i < testRounds; i++) {
            try {
                long startTime = System.currentTimeMillis();

                // 通过API间接测试数据库连接性能
                Response response = given()
                        .header(TENANT_HEADER, DEFAULT_TENANT_ID)
                        .queryParam("page", 0)
                        .queryParam("size", 1)
                        .when()
                        .get(PRODUCT_SERVICE_URL + "/api/products")
                        .then()
                        .statusCode(anyOf(is(200), is(404), is(500)))
                        .extract().response();

                long responseTime = System.currentTimeMillis() - startTime;

                if (response.getStatusCode() == 200) {
                    successCount++;
                    totalDbResponseTime += responseTime;
                    System.out.println("第" + (i + 1) + "次数据库查询响应时间: " + responseTime + "ms");
                }

                try {
                    Thread.sleep(200); // 间隔200ms
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }

            } catch (Exception e) {
                System.out.println("第" + (i + 1) + "次数据库查询失败: " + e.getMessage());
            }
        }

        if (successCount > 0) {
            double avgDbResponseTime = (double) totalDbResponseTime / successCount;

            System.out.println("=== 数据库连接性能测试结果 ===");
            System.out.println("成功查询次数: " + successCount + "/" + testRounds);
            System.out.println("平均数据库响应时间: " + String.format("%.2f", avgDbResponseTime) + "ms");

            // 验证数据库性能
            assertTrue(avgDbResponseTime < MAX_RESPONSE_TIME,
                    "数据库平均响应时间应小于" + MAX_RESPONSE_TIME + "ms，实际: " + avgDbResponseTime + "ms");

            System.out.println("✅ 数据库连接性能测试通过");
        } else {
            System.out.println("⚠️ 数据库连接性能测试失败，所有查询都失败");
        }
    }

    /**
     * 测试5：内存使用监控测试
     */
    @Test
    @Order(5)
    @DisplayName("5. 内存使用监控测试")
    void testMemoryUsageMonitoring() {
        System.out.println("测试5：内存使用监控测试");

        Runtime runtime = Runtime.getRuntime();

        // 记录测试开始时的内存状态
        long initialTotalMemory = runtime.totalMemory();
        long initialFreeMemory = runtime.freeMemory();
        long initialUsedMemory = initialTotalMemory - initialFreeMemory;

        System.out.println("=== 测试开始时内存状态 ===");
        System.out.println("总内存: " + (initialTotalMemory / 1024 / 1024) + " MB");
        System.out.println("空闲内存: " + (initialFreeMemory / 1024 / 1024) + " MB");
        System.out.println("已用内存: " + (initialUsedMemory / 1024 / 1024) + " MB");

        // 执行一些内存密集型操作
        try {
            for (int i = 0; i < 5; i++) {
                // 创建订单请求，模拟内存使用
                Map<String, Object> orderRequest = Map.of(
                        "businessId", "MEMORY_TEST_" + UUID.randomUUID().toString(),
                        "customerId", 1L,
                        "orderItems", IntStream.range(0, 100)
                                .mapToObj(j -> Map.of(
                                        "productId", (long) j,
                                        "quantity", 1,
                                        "unitPrice", 99.99
                                ))
                                .toList(),
                        "totalAmount", 9999.0,
                        "remark", "内存测试订单"
                );

                given()
                        .header(TENANT_HEADER, DEFAULT_TENANT_ID)
                        .contentType(ContentType.JSON)
                        .body(orderRequest)
                        .when()
                        .post(ORDER_SERVICE_URL + "/api/orders")
                        .then()
                        .statusCode(anyOf(is(200), is(201), is(400), is(422), is(500)));

                try {
                    Thread.sleep(1000); // 等待1秒
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }

        } catch (Exception e) {
            System.out.println("内存测试操作失败: " + e.getMessage());
        }

        // 记录测试结束时的内存状态
        System.gc(); // 建议垃圾回收
        try {
            Thread.sleep(1000); // 等待垃圾回收完成
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        long finalTotalMemory = runtime.totalMemory();
        long finalFreeMemory = runtime.freeMemory();
        long finalUsedMemory = finalTotalMemory - finalFreeMemory;

        System.out.println("=== 测试结束时内存状态 ===");
        System.out.println("总内存: " + (finalTotalMemory / 1024 / 1024) + " MB");
        System.out.println("空闲内存: " + (finalFreeMemory / 1024 / 1024) + " MB");
        System.out.println("已用内存: " + (finalUsedMemory / 1024 / 1024) + " MB");

        long memoryIncrease = finalUsedMemory - initialUsedMemory;
        System.out.println("内存增长: " + (memoryIncrease / 1024 / 1024) + " MB");

        // 验证内存使用是否合理（增长不超过100MB）
        assertTrue(memoryIncrease < 100 * 1024 * 1024,
                "内存增长应小于100MB，实际: " + (memoryIncrease / 1024 / 1024) + "MB");

        System.out.println("✅ 内存使用监控测试完成");
    }
}
